/**
 * Helper Utilities
 * Common utility functions used throughout the application
 */

/**
 * Format currency amount
 */
function formatCurrency(amount, currency = "USD") {
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: currency,
  }).format(amount || 0);
}

/**
 * Format date for display
 */
function formatDate(date, options = {}) {
  const defaultOptions = {
    year: "numeric",
    month: "short",
    day: "numeric",
  };

  return new Date(date).toLocaleDateString("en-US", {
    ...defaultOptions,
    ...options,
  });
}

/**
 * Format date and time for display
 */
function formatDateTime(date, options = {}) {
  const defaultOptions = {
    year: "numeric",
    month: "short",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  };

  return new Date(date).toLocaleString("en-US", {
    ...defaultOptions,
    ...options,
  });
}

/**
 * Format date for HTML input
 */
function formatDateForInput(date) {
  return new Date(date).toISOString().split("T")[0];
}

/**
 * Validate email format
 */
function isValidEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Validate phone number format (Egyptian numbers)
 */
function isValidPhone(phone) {
  // Remove all non-digit characters
  const cleanPhone = phone.replace(/\D/g, "");

  // Egyptian mobile numbers: 01xxxxxxxxx (11 digits total)
  // Egyptian landline numbers: 0xxxxxxxx (9-10 digits total)

  // Check for Egyptian mobile numbers (01 followed by 9 digits)
  const mobilePattern = /^01[0-9]{9}$/;
  if (mobilePattern.test(cleanPhone)) {
    return true;
  }

  // Check for Egyptian landline numbers (0 followed by 8-9 digits)
  const landlinePattern = /^0[2-9][0-9]{7,8}$/;
  if (landlinePattern.test(cleanPhone)) {
    return true;
  }

  return false;
}

/**
 * Format phone number for display
 */
function formatPhone(phone) {
  const cleanPhone = phone.replace(/\D/g, "");

  // Format Egyptian mobile numbers: 01x xxxx xxxx
  if (cleanPhone.length === 11 && cleanPhone.startsWith("01")) {
    return `${cleanPhone.slice(0, 3)} ${cleanPhone.slice(
      3,
      7
    )} ${cleanPhone.slice(7)}`;
  }

  // Format Egyptian landline numbers: 0xx xxx xxxx or 0xx xxxx xxxx
  if (
    cleanPhone.length >= 9 &&
    cleanPhone.length <= 10 &&
    cleanPhone.startsWith("0") &&
    !cleanPhone.startsWith("01")
  ) {
    if (cleanPhone.length === 9) {
      return `${cleanPhone.slice(0, 3)} ${cleanPhone.slice(
        3,
        6
      )} ${cleanPhone.slice(6)}`;
    } else {
      return `${cleanPhone.slice(0, 3)} ${cleanPhone.slice(
        3,
        7
      )} ${cleanPhone.slice(7)}`;
    }
  }

  return phone; // Return original if no formatting rule matches
}

/**
 * Escape HTML to prevent XSS
 */
function escapeHtml(text) {
  const div = document.createElement("div");
  div.textContent = text;
  return div.innerHTML;
}

/**
 * Debounce function to limit function calls
 */
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

/**
 * Generate unique ID
 */
function generateId() {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

/**
 * Deep clone an object
 */
function deepClone(obj) {
  if (obj === null || typeof obj !== "object") {
    return obj;
  }

  if (obj instanceof Date) {
    return new Date(obj.getTime());
  }

  if (obj instanceof Array) {
    return obj.map((item) => deepClone(item));
  }

  if (typeof obj === "object") {
    const clonedObj = {};
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key]);
      }
    }
    return clonedObj;
  }
}

/**
 * Calculate percentage
 */
function calculatePercentage(value, total) {
  if (total === 0) return 0;
  return (value / total) * 100;
}

/**
 * Round to specified decimal places
 */
function roundTo(number, decimals = 2) {
  return Math.round(number * Math.pow(10, decimals)) / Math.pow(10, decimals);
}

/**
 * Check if value is empty (null, undefined, empty string, empty array)
 */
function isEmpty(value) {
  if (value === null || value === undefined) return true;
  if (typeof value === "string") return value.trim().length === 0;
  if (Array.isArray(value)) return value.length === 0;
  if (typeof value === "object") return Object.keys(value).length === 0;
  return false;
}

/**
 * Capitalize first letter of string
 */
function capitalize(str) {
  if (!str) return "";
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
}

/**
 * Convert string to title case
 */
function toTitleCase(str) {
  if (!str) return "";
  return str.replace(
    /\w\S*/g,
    (txt) => txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
  );
}

/**
 * Download data as file
 */
function downloadFile(content, filename, contentType = "text/plain") {
  const blob = new Blob([content], { type: contentType });
  const link = document.createElement("a");

  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob);
    link.setAttribute("href", url);
    link.setAttribute("download", filename);
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }
}

/**
 * Show notification (can be enhanced with better UI)
 */
function showNotification(message, type = "info", duration = 3000) {
  // Simple implementation - can be enhanced with toast notifications
  console.log(`${type.toUpperCase()}: ${message}`);

  // You can implement a better notification system here
  if (type === "error") {
    alert(`Error: ${message}`);
  } else if (type === "success") {
    // For success messages, you might want a less intrusive notification
    console.log(`Success: ${message}`);
  }
}

/**
 * Validate required fields in a form
 */
function validateRequiredFields(formData, requiredFields) {
  const errors = [];

  requiredFields.forEach((field) => {
    if (isEmpty(formData[field])) {
      errors.push(
        `${toTitleCase(field.replace(/([A-Z])/g, " $1"))} is required`
      );
    }
  });

  return errors;
}

/**
 * Format file size
 */
function formatFileSize(bytes) {
  if (bytes === 0) return "0 Bytes";

  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
}

/**
 * Get date range for common periods
 */
function getDateRange(period) {
  const today = new Date();
  const ranges = {
    today: {
      start: new Date(today.getFullYear(), today.getMonth(), today.getDate()),
      end: new Date(
        today.getFullYear(),
        today.getMonth(),
        today.getDate(),
        23,
        59,
        59
      ),
    },
    yesterday: {
      start: new Date(
        today.getFullYear(),
        today.getMonth(),
        today.getDate() - 1
      ),
      end: new Date(
        today.getFullYear(),
        today.getMonth(),
        today.getDate() - 1,
        23,
        59,
        59
      ),
    },
    thisWeek: {
      start: new Date(
        today.getFullYear(),
        today.getMonth(),
        today.getDate() - today.getDay()
      ),
      end: new Date(
        today.getFullYear(),
        today.getMonth(),
        today.getDate() - today.getDay() + 6,
        23,
        59,
        59
      ),
    },
    thisMonth: {
      start: new Date(today.getFullYear(), today.getMonth(), 1),
      end: new Date(today.getFullYear(), today.getMonth() + 1, 0, 23, 59, 59),
    },
    lastMonth: {
      start: new Date(today.getFullYear(), today.getMonth() - 1, 1),
      end: new Date(today.getFullYear(), today.getMonth(), 0, 23, 59, 59),
    },
  };

  return ranges[period] || ranges.today;
}

/**
 * Local storage helpers
 */
const storage = {
  set: (key, value) => {
    try {
      localStorage.setItem(key, JSON.stringify(value));
    } catch (error) {
      console.error("Error saving to localStorage:", error);
    }
  },

  get: (key, defaultValue = null) => {
    try {
      const item = localStorage.getItem(key);
      return item ? JSON.parse(item) : defaultValue;
    } catch (error) {
      console.error("Error reading from localStorage:", error);
      return defaultValue;
    }
  },

  remove: (key) => {
    try {
      localStorage.removeItem(key);
    } catch (error) {
      console.error("Error removing from localStorage:", error);
    }
  },

  clear: () => {
    try {
      localStorage.clear();
    } catch (error) {
      console.error("Error clearing localStorage:", error);
    }
  },
};

// Export functions for use in other modules
if (typeof module !== "undefined" && module.exports) {
  module.exports = {
    formatCurrency,
    formatDate,
    formatDateTime,
    formatDateForInput,
    isValidEmail,
    isValidPhone,
    formatPhone,
    escapeHtml,
    debounce,
    generateId,
    deepClone,
    calculatePercentage,
    roundTo,
    isEmpty,
    capitalize,
    toTitleCase,
    downloadFile,
    showNotification,
    validateRequiredFields,
    formatFileSize,
    getDateRange,
    storage,
  };
} else {
  // Make functions available globally in browser
  window.helpers = {
    formatCurrency,
    formatDate,
    formatDateTime,
    formatDateForInput,
    isValidEmail,
    isValidPhone,
    formatPhone,
    escapeHtml,
    debounce,
    generateId,
    deepClone,
    calculatePercentage,
    roundTo,
    isEmpty,
    capitalize,
    toTitleCase,
    downloadFile,
    showNotification,
    validateRequiredFields,
    formatFileSize,
    getDateRange,
    storage,
  };
}
