/**
 * Dashboard Controller
 * Handles dashboard statistics and analytics display
 */
class DashboardController {
  constructor() {
    console.log("Initializing Dashboard Controller...");
    this.currentDate = new Date();
    this.stats = {
      todaySales: 0,
      todayOrders: 0,
      totalCustomers: 0,
      avgOrder: 0,
    };
    this.recentOrders = [];
    this.isLoading = false;

    this.initializeEventListeners();
    this.loadDashboardData();
  }

  /**
   * Initialize event listeners for dashboard
   */
  initializeEventListeners() {
    // Date filter
    const dashboardDate = document.getElementById("dashboard-date");
    if (dashboardDate) {
      // Set default to today
      dashboardDate.value = this.formatDateForInput(this.currentDate);
      dashboardDate.addEventListener("change", (e) => {
        this.currentDate = new Date(e.target.value);
        this.loadDashboardData();
      });
    }

    // Refresh button
    const refreshBtn = document.getElementById("refresh-dashboard");
    if (refreshBtn) {
      refreshBtn.addEventListener("click", () => this.loadDashboardData());
    }
  }

  /**
   * Load all dashboard data
   */
  async loadDashboardData() {
    try {
      this.setLoading(true);

      // Load statistics
      await this.loadStats();

      // Load recent orders
      await this.loadRecentOrders();

      // Update displays
      this.updateStatsDisplay();
      this.updateRecentOrdersDisplay();
    } catch (error) {
      console.error("Error loading dashboard data:", error);
      this.showAlert("Error loading dashboard data", "error");
    } finally {
      this.setLoading(false);
    }
  }

  /**
   * Load dashboard statistics
   */
  async loadStats() {
    try {
      // Get date range for today
      const startOfDay = new Date(this.currentDate);
      startOfDay.setHours(0, 0, 0, 0);

      const endOfDay = new Date(this.currentDate);
      endOfDay.setHours(23, 59, 59, 999);

      // Get today's completed orders
      const todayOrders = await dbController.find("orders", {
        orderDate: { $gte: startOfDay, $lte: endOfDay },
        status: "completed",
      });

      // Get total customers count
      const totalCustomers = await dbController.count("customers");

      // Calculate statistics
      const todaySales = todayOrders.reduce(
        (sum, order) => sum + (order.total || 0),
        0
      );
      const avgOrder =
        todayOrders.length > 0 ? todaySales / todayOrders.length : 0;

      this.stats = {
        todaySales: todaySales,
        todayOrders: todayOrders.length,
        totalCustomers: totalCustomers,
        avgOrder: avgOrder,
      };
    } catch (error) {
      console.error("Error loading stats:", error);
      throw error;
    }
  }

  /**
   * Load recent orders for display
   */
  async loadRecentOrders() {
    try {
      // Get last 10 orders
      this.recentOrders = await dbController.find(
        "orders",
        {},
        { orderDate: -1 }
      );
      this.recentOrders = this.recentOrders.slice(0, 10);
    } catch (error) {
      console.error("Error loading recent orders:", error);
      throw error;
    }
  }

  /**
   * Update statistics display
   */
  updateStatsDisplay() {
    // Today's sales
    const todaySalesEl = document.getElementById("today-sales");
    if (todaySalesEl) {
      todaySalesEl.textContent = this.formatCurrency(this.stats.todaySales);
    }

    // Today's orders
    const todayOrdersEl = document.getElementById("today-orders");
    if (todayOrdersEl) {
      todayOrdersEl.textContent = this.stats.todayOrders.toString();
    }

    // Total customers
    const totalCustomersEl = document.getElementById("total-customers");
    if (totalCustomersEl) {
      totalCustomersEl.textContent = this.stats.totalCustomers.toString();
    }

    // Average order
    const avgOrderEl = document.getElementById("avg-order");
    if (avgOrderEl) {
      avgOrderEl.textContent = this.formatCurrency(this.stats.avgOrder);
    }
  }

  /**
   * Update recent orders display
   */
  updateRecentOrdersDisplay() {
    const recentOrdersList = document.getElementById("recent-orders-list");
    if (!recentOrdersList) return;

    if (this.recentOrders.length === 0) {
      recentOrdersList.innerHTML = `
                <div class="text-center" style="padding: 20px;">
                    <p class="text-muted">No recent orders found.</p>
                </div>
            `;
      return;
    }

    recentOrdersList.innerHTML = this.recentOrders
      .map(
        (order) => `
            <div class="recent-order">
                <div class="order-info">
                    <div class="order-customer">${this.escapeHtml(
                      order.customerName
                    )}</div>
                    <div class="order-time">
                        ${this.formatDateTime(order.orderDate)} • 
                        Order #${this.escapeHtml(order.orderNumber)}
                    </div>
                </div>
                <div class="order-total">${this.formatCurrency(
                  order.total
                )}</div>
            </div>
        `
      )
      .join("");
  }

  /**
   * Get sales data for a date range (for charts/analytics)
   */
  async getSalesData(days = 7) {
    try {
      const salesData = [];
      const today = new Date();

      for (let i = days - 1; i >= 0; i--) {
        const date = new Date(today);
        date.setDate(date.getDate() - i);

        const startOfDay = new Date(date);
        startOfDay.setHours(0, 0, 0, 0);

        const endOfDay = new Date(date);
        endOfDay.setHours(23, 59, 59, 999);

        const dayOrders = await dbController.find("orders", {
          orderDate: { $gte: startOfDay, $lte: endOfDay },
          status: "completed",
        });

        const daySales = dayOrders.reduce(
          (sum, order) => sum + (order.total || 0),
          0
        );

        salesData.push({
          date: this.formatDate(date),
          sales: daySales,
          orders: dayOrders.length,
        });
      }

      return salesData;
    } catch (error) {
      console.error("Error getting sales data:", error);
      return [];
    }
  }

  /**
   * Get top selling items
   */
  async getTopSellingItems(limit = 5) {
    try {
      const orders = await dbController.find("orders", { status: "completed" });
      const itemSales = {};

      // Aggregate item sales
      orders.forEach((order) => {
        if (order.items && Array.isArray(order.items)) {
          order.items.forEach((item) => {
            const itemName = item.name;
            if (!itemSales[itemName]) {
              itemSales[itemName] = {
                name: itemName,
                quantity: 0,
                revenue: 0,
              };
            }
            itemSales[itemName].quantity += item.quantity || 0;
            itemSales[itemName].revenue +=
              (item.quantity || 0) * (item.price || 0);
          });
        }
      });

      // Convert to array and sort by quantity
      const topItems = Object.values(itemSales)
        .sort((a, b) => b.quantity - a.quantity)
        .slice(0, limit);

      return topItems;
    } catch (error) {
      console.error("Error getting top selling items:", error);
      return [];
    }
  }

  /**
   * Get customer statistics
   */
  async getCustomerStats() {
    try {
      const customers = await dbController.find("customers");
      const orders = await dbController.find("orders", { status: "completed" });

      // Calculate customer spending
      const customerSpending = {};
      orders.forEach((order) => {
        if (!customerSpending[order.customerId]) {
          customerSpending[order.customerId] = {
            customerId: order.customerId,
            customerName: order.customerName,
            totalSpent: 0,
            orderCount: 0,
          };
        }
        customerSpending[order.customerId].totalSpent += order.total || 0;
        customerSpending[order.customerId].orderCount += 1;
      });

      // Get top customers by spending
      const topCustomers = Object.values(customerSpending)
        .sort((a, b) => b.totalSpent - a.totalSpent)
        .slice(0, 5);

      return {
        totalCustomers: customers.length,
        activeCustomers: Object.keys(customerSpending).length,
        topCustomers: topCustomers,
      };
    } catch (error) {
      console.error("Error getting customer stats:", error);
      return {
        totalCustomers: 0,
        activeCustomers: 0,
        topCustomers: [],
      };
    }
  }

  /**
   * Export dashboard data to CSV
   */
  async exportDashboardData() {
    try {
      const salesData = await this.getSalesData(30); // Last 30 days
      const topItems = await this.getTopSellingItems(10);
      const customerStats = await this.getCustomerStats();

      // Create CSV content
      let csvContent = "Dashboard Export\n\n";

      // Sales data
      csvContent += "Daily Sales (Last 30 Days)\n";
      csvContent += "Date,Sales,Orders\n";
      salesData.forEach((day) => {
        csvContent += `${day.date},${day.sales.toFixed(2)},${day.orders}\n`;
      });

      csvContent += "\n\nTop Selling Items\n";
      csvContent += "Item Name,Quantity Sold,Revenue\n";
      topItems.forEach((item) => {
        csvContent += `"${item.name}",${item.quantity},${item.revenue.toFixed(
          2
        )}\n`;
      });

      csvContent += "\n\nTop Customers\n";
      csvContent += "Customer Name,Total Spent,Order Count\n";
      customerStats.topCustomers.forEach((customer) => {
        csvContent += `"${customer.customerName}",${customer.totalSpent.toFixed(
          2
        )},${customer.orderCount}\n`;
      });

      // Download CSV
      this.downloadCSV(
        csvContent,
        `dashboard-export-${this.formatDate(new Date())}.csv`
      );
    } catch (error) {
      console.error("Error exporting dashboard data:", error);
      this.showAlert("Error exporting dashboard data", "error");
    }
  }

  /**
   * Utility methods
   */
  formatCurrency(amount) {
    return `$${(amount || 0).toFixed(2)}`;
  }

  formatDate(date) {
    return date.toLocaleDateString();
  }

  formatDateTime(date) {
    return new Date(date).toLocaleString();
  }

  formatDateForInput(date) {
    return date.toISOString().split("T")[0];
  }

  downloadCSV(content, filename) {
    const blob = new Blob([content], { type: "text/csv;charset=utf-8;" });
    const link = document.createElement("a");

    if (link.download !== undefined) {
      const url = URL.createObjectURL(blob);
      link.setAttribute("href", url);
      link.setAttribute("download", filename);
      link.style.visibility = "hidden";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  }

  setLoading(loading) {
    this.isLoading = loading;
    // You can add loading indicators here
  }

  showAlert(message, type = "info") {
    if (type === "error") {
      alert("Error: " + message);
    } else if (type === "success") {
      alert("Success: " + message);
    } else {
      alert(message);
    }
  }

  escapeHtml(text) {
    const div = document.createElement("div");
    div.textContent = text;
    return div.innerHTML;
  }
}

// Initialize dashboard controller when DOM is loaded
let dashboardController;
if (typeof document !== "undefined") {
  document.addEventListener("DOMContentLoaded", () => {
    dashboardController = new DashboardController();
    window.dashboardController = dashboardController;
  });
}

// Export for use in other modules
if (typeof module !== "undefined" && module.exports) {
  module.exports = DashboardController;
} else {
  window.DashboardController = DashboardController;
}
