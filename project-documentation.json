{"project": {"name": "<PERSON><PERSON><PERSON>", "version": "1.0.0", "description": "A comprehensive desktop cashier application built with Electron.js for small businesses and restaurants, with enhanced support for Egyptian phone numbers", "type": "Desktop Application", "platform": "Cross-platform (Windows, macOS, Linux)", "license": "MIT"}, "technology_stack": {"frontend": {"html": "HTML5", "css": "CSS3 with CSS Variables", "javascript": "Vanilla JavaScript (ES6+)", "styling_approach": "Custom CSS with component-based architecture"}, "backend": {"runtime": "Node.js (embedded in Electron)", "framework": "Electron.js", "database": "NeDB (embedded NoSQL database)", "file_system": "Node.js fs module"}, "build_tools": {"bundler": "Electron Builder", "package_manager": "npm", "development": "Electron in development mode"}, "external_libraries": {"jspdf": "PDF generation for receipts", "html2canvas": "HTML to canvas conversion", "nedb": "Embedded database"}}, "architecture": {"pattern": "MVC (Model-View-Controller)", "structure": "Modular component-based architecture", "data_flow": "Event-driven with IPC communication", "validation": "Real-time client-side validation", "notifications": "Toast notification system"}, "file_structure": {"root_files": [{"file": "package.json", "purpose": "Project configuration and dependencies", "key_features": ["Scripts definition", "Electron builder config", "Dependencies management"]}, {"file": "README.md", "purpose": "Project documentation and usage guide", "sections": ["Features", "Installation", "Usage", "Egyptian phone support", "Testing"]}, {"file": "test-phone-validation.html", "purpose": "Standalone testing for Egyptian phone validation", "features": ["Interactive testing", "Test cases", "Real-time validation demo"]}], "src_directory": {"main_process": {"file": "src/main.js", "purpose": "Main Electron process - application entry point", "responsibilities": ["Window creation and management", "Menu system setup", "IPC handlers for database operations", "Application lifecycle management"], "key_features": ["BrowserWindow configuration", "<PERSON><PERSON> with keyboard shortcuts", "Database operation handlers", "Theme and settings management"]}, "models": {"directory": "src/models/", "files": [{"file": "Customer.js", "purpose": "Customer data model with validation", "features": ["Egyptian phone number validation", "Email validation", "Data sanitization", "Phone number formatting", "Customer statistics calculation", "Search query generation"], "validation_rules": ["Name: required, 2-100 characters", "Phone: required, Egyptian format (01xxxxxxxxx or 0xxxxxxxx)", "Email: optional, valid email format", "Address: optional, max 500 characters", "Notes: optional, max 1000 characters"]}, {"file": "Order.js", "purpose": "Order data model and calculations", "features": ["Order item management", "Price calculations", "Tax and discount handling", "Order status management", "Receipt generation data"]}]}, "controllers": {"directory": "src/controllers/", "files": [{"file": "database.js", "purpose": "Database operations controller", "features": ["NeDB database initialization", "CRUD operations", "Data persistence", "Backup and restore functionality"]}, {"file": "customers.js", "purpose": "Customer management controller", "features": ["Customer CRUD operations", "Real-time search and filtering", "Form validation integration", "Egyptian phone formatting", "Modal management", "Enhanced notification system"], "ui_interactions": ["Add/Edit customer modal", "Customer list display", "Search functionality", "Delete confirmation", "View customer details"]}, {"file": "orders.js", "purpose": "Order management controller", "features": ["Order creation and editing", "Item management", "Price calculations", "Receipt printing", "Order status updates"]}, {"file": "dashboard.js", "purpose": "Dashboard analytics controller", "features": ["Sales statistics calculation", "Date range filtering", "Chart data preparation", "Performance metrics"]}]}, "utils": {"directory": "src/utils/", "files": [{"file": "helpers.js", "purpose": "Common utility functions", "functions": ["formatCurrency() - Currency formatting", "formatDate() - Date formatting", "isValidEmail() - Email validation", "isValidPhone() - Egyptian phone validation", "formatPhone() - Egyptian phone formatting", "escapeHtml() - XSS prevention", "debounce() - Function call limiting", "generateId() - Unique ID generation", "deepClone() - Object cloning", "storage - LocalStorage helpers"]}, {"file": "notifications.js", "purpose": "Enhanced notification system", "features": ["Toast notifications", "Multiple notification types (success, error, warning, info)", "Auto-dismiss functionality", "Manual dismissal", "Animation system", "Validation error display", "Responsive design"], "notification_types": {"success": "Green notifications for successful operations", "error": "Red notifications for errors (persistent by default)", "warning": "Yellow notifications for warnings", "info": "Blue notifications for information"}}, {"file": "validation.js", "purpose": "Real-time form validation system", "features": ["Real-time field validation", "Visual feedback (red/green borders)", "Custom validation rules", "Egyptian phone validation", "Email validation", "Required field validation", "Form-wide validation", "Error message management"], "validation_rules": ["required - Field must not be empty", "minLength - Minimum character length", "maxLength - Maximum character length", "email - Valid email format", "phone - Egyptian phone format", "pattern - Custom regex pattern", "custom - Custom validation function"]}, {"file": "phoneFormatter.js", "purpose": "Egyptian phone number formatting", "features": ["Real-time formatting as user types", "Egyptian mobile format (01x xxxx xxxx)", "Egyptian landline format (0xx xxx xxxx)", "Input restriction (numbers only)", "Cursor position management", "Format validation", "Unformatted number extraction"], "supported_formats": {"mobile": "01x xxxx xxxx (11 digits)", "landline_9": "0xx xxx xxxx (9 digits)", "landline_10": "0xx xxxx xxxx (10 digits)"}}, {"file": "theme.js", "purpose": "Theme management system", "features": ["Light/Dark theme toggle", "System preference detection", "Theme persistence", "CSS variable management"]}]}, "views": {"directory": "src/views/", "main_file": {"file": "index.html", "purpose": "Main application interface", "structure": ["Header with app title and theme toggle", "Navigation sidebar with menu items", "Main content area with multiple views", "Modal overlay for forms and dialogs"], "views": ["Dashboard - Sales analytics and statistics", "Customers - Customer management interface", "Orders - Order history and management", "New Order - Order creation interface"], "included_scripts": ["Customer.js - Customer model", "Order.js - Order model", "database.js - Database controller", "helpers.js - Utility functions", "notifications.js - Notification system", "validation.js - Validation system", "phoneFormatter.js - Phone formatting", "theme.js - Theme management", "customers.js - Customer controller", "orders.js - Order controller", "dashboard.js - Dashboard controller", "app.js - Main application logic"]}, "styles": {"directory": "src/views/styles/", "files": [{"file": "main.css", "purpose": "Main application styles", "features": ["Layout and grid system", "Typography", "Base component styles", "Responsive design"]}, {"file": "components.css", "purpose": "Component-specific styles", "components": ["Buttons and form elements", "Cards and list items", "Modals and overlays", "Navigation components"]}, {"file": "themes.css", "purpose": "Theme definitions", "themes": ["Light theme variables", "Dark theme variables", "Theme transition animations"]}, {"file": "notifications.css", "purpose": "Notification system styles", "features": ["Toast notification styling", "Animation keyframes", "Responsive notification container", "Form validation styling", "Success/error state styling", "Dark theme support"]}]}, "scripts": {"directory": "src/views/scripts/", "files": [{"file": "app.js", "purpose": "Main application logic", "features": ["Application initialization", "Navigation system", "Menu integration", "Keyboard shortcuts", "View management", "Data export/import", "Enhanced notification integration"], "keyboard_shortcuts": {"Ctrl+1": "Dashboard", "Ctrl+2": "Customers", "Ctrl+3": "Orders", "Ctrl+N": "New Order", "Ctrl+T": "Toggle Theme", "Ctrl+E": "Export Data", "Escape": "Close Modal"}}]}}}}, "features": {"customer_management": {"description": "Complete customer lifecycle management", "capabilities": ["Add new customers with Egyptian phone validation", "Edit existing customer information", "Delete customers (with order dependency check)", "Search customers by name, phone, or email", "View customer statistics and order history", "Real-time form validation with visual feedback"], "validation_features": ["Egyptian mobile numbers: 01xxxxxxxxx (11 digits)", "Egyptian landline numbers: 0xxxxxxxx (9-10 digits)", "Email format validation (optional field)", "Name length validation (2-100 characters)", "Address and notes length limits"], "ui_enhancements": ["Real-time phone number formatting", "Toast notifications for success/error states", "Visual field validation (red/green borders)", "Modal-based forms with enhanced UX"]}, "order_management": {"description": "Complete order processing system", "capabilities": ["Create new orders with customer selection", "Add multiple items with quantities and prices", "Calculate subtotals, taxes, and discounts", "Support multiple payment methods", "Order status tracking", "Receipt generation and printing"], "calculations": ["Automatic subtotal calculation", "Tax calculation based on configurable rates", "Discount application (percentage or fixed amount)", "Total amount calculation", "Change calculation for cash payments"]}, "dashboard_analytics": {"description": "Business intelligence and reporting", "metrics": ["Daily sales totals", "Number of orders per day", "Average order value", "Customer count", "Best-selling items", "Sales trends over time"], "filtering": ["Date range selection", "Customer-specific analytics", "Order status filtering", "Payment method breakdown"]}, "egyptian_phone_support": {"description": "Comprehensive Egyptian phone number handling", "mobile_carriers": {"vodafone": "010xxxxxxxx", "orange": "011xxxxxxxx, 012xxxxxxxx", "etisalat": "011xxxxxxxx", "we": "015xxxxxxxx"}, "landline_codes": {"cairo": "02xxxxxxxx", "alexandria": "03xxxxxxxx", "other_governorates": "04x-09xxxxxxxx"}, "formatting_examples": ["*********** → 012 7999 5486", "0503612247 → ************", "0223456789 → 022 3456 789"], "validation_logic": ["Real-time validation as user types", "Format checking with regex patterns", "Length validation (9-11 digits)", "Prefix validation (must start with 0)", "Carrier code validation"]}}, "programming_logic": {"data_flow": {"description": "Application data flow and state management", "flow": ["User interaction triggers event handlers", "Controllers process business logic", "Models validate and format data", "Database operations persist changes", "UI updates reflect new state", "Notifications provide user feedback"]}, "validation_system": {"description": "Multi-layer validation approach", "layers": ["Client-side real-time validation", "Model-level validation rules", "Database constraint validation", "UI feedback and error display"], "validation_flow": ["User input triggers validation", "Field-level rules are checked", "Visual feedback is provided immediately", "Form submission validates all fields", "Server-side validation as backup"]}, "notification_system": {"description": "Enhanced user feedback system", "types": {"success": {"color": "Green", "duration": "3 seconds", "use_case": "Successful operations"}, "error": {"color": "Red", "duration": "Persistent (manual dismiss)", "use_case": "Validation errors, operation failures"}, "warning": {"color": "Yellow", "duration": "5 seconds", "use_case": "Important notices"}, "info": {"color": "Blue", "duration": "4 seconds", "use_case": "General information"}}, "features": ["Auto-stacking of multiple notifications", "Responsive positioning", "Animation effects (slide-in/fade-out)", "Click-to-dismiss functionality", "Maximum notification limit"]}}, "database_schema": {"customers": {"fields": ["_id: Unique identifier (auto-generated)", "name: Customer name (string, required)", "phone: Phone number (string, required, Egyptian format)", "email: Email address (string, optional)", "address: Physical address (string, optional)", "notes: Additional notes (string, optional)", "createdAt: Creation timestamp (Date)", "updatedAt: Last update timestamp (Date)"], "indexes": ["name", "phone"], "relationships": ["One-to-many with orders"]}, "orders": {"fields": ["_id: Unique identifier (auto-generated)", "customerId: Reference to customer (string, required)", "items: Array of order items (array, required)", "subtotal: Subtotal amount (number)", "tax: Tax amount (number)", "discount: Discount amount (number)", "total: Total amount (number)", "paymentMethod: Payment method (string)", "status: Order status (string)", "orderDate: Order date (Date)", "notes: Order notes (string, optional)"], "indexes": ["customerId", "orderDate", "status"], "relationships": ["Many-to-one with customers"]}, "settings": {"fields": ["_id: Unique identifier (auto-generated)", "key: Setting key (string, unique)", "value: Setting value (any type)", "updatedAt: Last update timestamp (Date)"], "common_settings": ["theme: Application theme preference", "taxRate: Default tax rate", "currency: Currency symbol", "companyInfo: Business information"]}}, "development_workflow": {"setup": {"prerequisites": ["Node.js version 16 or higher", "npm package manager", "Git for version control"], "installation_steps": ["Clone repository", "Run 'npm install' to install dependencies", "Run 'npm run dev' for development mode", "Run 'npm start' for production mode"]}, "build_process": {"development": {"command": "npm run dev", "features": ["DevTools enabled", "Hot reload for changes", "Debug console access", "Source maps enabled"]}, "production": {"commands": ["npm run build - General build", "npm run build:win - Windows build", "npm run pack - Portable executable", "npm run dist - Distribution package"], "output": ["Executable files in dist/ directory", "Installer packages", "Portable applications"]}}, "code_standards": {"javascript": ["ES6+ syntax and features", "Modular architecture", "JSDoc comments for functions", "Error handling with try-catch", "Consistent naming conventions"], "css": ["CSS Variables for theming", "BEM methodology for class naming", "Responsive design principles", "Component-based styling"], "html": ["Semantic HTML5 elements", "Accessibility attributes", "Progressive enhancement", "Clean markup structure"]}}, "testing_strategy": {"validation_testing": {"phone_numbers": {"test_file": "test-phone-validation.html", "test_cases": ["Valid mobile: ***********", "Valid landline: 0503612247", "Invalid short: 012345", "Invalid long: 012345678901", "Invalid prefix: 09123456789"], "testing_features": ["Interactive input testing", "Real-time validation feedback", "Format verification", "Edge case handling"]}, "form_validation": ["Required field validation", "Email format validation", "Length limit validation", "Real-time feedback testing", "Visual state verification"]}, "manual_testing": {"customer_operations": ["Add customer with valid Egyptian phone", "Edit customer information", "Delete customer (with/without orders)", "Search functionality", "Phone number formatting verification"], "notification_testing": ["Success notification display", "Error notification persistence", "Auto-dismiss functionality", "Multiple notification stacking", "Manual dismissal"], "ui_testing": ["Theme switching", "Responsive design", "Keyboard navigation", "Modal functionality", "Form submission flow"]}}, "deployment": {"target_platforms": ["Windows (primary target)", "macOS (cross-platform support)", "Linux (cross-platform support)"], "distribution_methods": ["Portable executable (.exe)", "Installer package", "App store distribution (future)", "Direct download"], "system_requirements": {"minimum": ["Windows 10 or equivalent", "4GB RAM", "100MB disk space", "1024x768 screen resolution"], "recommended": ["Windows 11 or equivalent", "8GB RAM", "500MB disk space", "1920x1080 screen resolution"]}}, "recent_improvements": {"egyptian_phone_support": {"date": "2024", "changes": ["Added Egyptian phone number validation", "Implemented real-time phone formatting", "Updated error messages for clarity", "Added support for all Egyptian carriers"]}, "enhanced_notifications": {"date": "2024", "changes": ["Replaced basic alerts with toast notifications", "Added multiple notification types", "Implemented auto-dismiss functionality", "Added responsive design support"]}, "real_time_validation": {"date": "2024", "changes": ["Added real-time form validation", "Implemented visual feedback system", "Created custom validation rules", "Enhanced user experience"]}}, "future_enhancements": {"planned_features": ["Inventory management system", "Advanced reporting and analytics", "Multi-user support with roles", "Cloud synchronization", "Barcode scanning integration", "Integration with accounting software", "Mobile app companion", "Advanced receipt customization"], "technical_improvements": ["Database migration to SQLite", "API integration capabilities", "Enhanced security features", "Performance optimizations", "Automated testing suite", "Continuous integration setup"]}}