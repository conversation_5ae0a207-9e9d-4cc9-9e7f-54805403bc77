{"name": "kashier-desktop", "version": "1.0.0", "description": "A desktop cashier application built with Electron.js", "main": "src/main.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "build:win": "electron-builder --win", "dist": "electron-builder --publish=never", "pack": "electron-builder --dir", "postinstall": "electron-builder install-app-deps"}, "keywords": ["electron", "cashier", "pos", "desktop", "javascript"], "author": "Kashier Team", "license": "MIT", "devDependencies": {"electron": "^27.0.0", "electron-builder": "^24.6.4"}, "dependencies": {"nedb": "^1.8.0", "jspdf": "^2.5.1", "html2canvas": "^1.4.1"}, "build": {"appId": "com.kashier.desktop", "productName": "<PERSON><PERSON><PERSON>", "directories": {"output": "dist"}, "files": ["src/**/*", "assets/**/*", "node_modules/**/*"], "win": {"target": "portable"}, "portable": {"artifactName": "<PERSON><PERSON><PERSON>-Desktop-${version}.exe"}}}