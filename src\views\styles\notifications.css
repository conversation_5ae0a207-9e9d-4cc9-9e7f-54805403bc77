/**
 * Notification System Styles
 */

.notification-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 10000;
  max-width: 400px;
  pointer-events: none;
}

.notification {
  background: var(--bg-color, #fff);
  border: 1px solid var(--border-color, #ddd);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  margin-bottom: 10px;
  opacity: 0;
  transform: translateX(100%);
  transition: all 0.3s ease;
  pointer-events: auto;
  max-width: 100%;
  word-wrap: break-word;
}

.notification.show {
  opacity: 1;
  transform: translateX(0);
}

.notification.hide {
  opacity: 0;
  transform: translateX(100%);
}

.notification-content {
  display: flex;
  align-items: flex-start;
  padding: 12px 16px;
  gap: 10px;
}

.notification-icon {
  font-size: 16px;
  font-weight: bold;
  flex-shrink: 0;
  margin-top: 2px;
}

.notification-message {
  flex: 1;
  font-size: 14px;
  line-height: 1.4;
  color: var(--text-color, #333);
}

.notification-close {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  padding: 0;
  margin-left: 8px;
  color: var(--text-color-muted, #666);
  flex-shrink: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.notification-close:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

/* Notification Types */
.notification-success {
  border-left: 4px solid #28a745;
  background-color: #d4edda;
}

.notification-success .notification-icon {
  color: #28a745;
}

.notification-success .notification-message {
  color: #155724;
}

.notification-error {
  border-left: 4px solid #dc3545;
  background-color: #f8d7da;
}

.notification-error .notification-icon {
  color: #dc3545;
}

.notification-error .notification-message {
  color: #721c24;
}

.notification-warning {
  border-left: 4px solid #ffc107;
  background-color: #fff3cd;
}

.notification-warning .notification-icon {
  color: #856404;
}

.notification-warning .notification-message {
  color: #856404;
}

.notification-info {
  border-left: 4px solid #17a2b8;
  background-color: #d1ecf1;
}

.notification-info .notification-icon {
  color: #17a2b8;
}

.notification-info .notification-message {
  color: #0c5460;
}

/* Dark theme support */
.dark-theme .notification {
  background: var(--bg-color-dark, #2d3748);
  border-color: var(--border-color-dark, #4a5568);
  color: var(--text-color-dark, #e2e8f0);
}

.dark-theme .notification-message {
  color: var(--text-color-dark, #e2e8f0);
}

.dark-theme .notification-close {
  color: var(--text-color-muted-dark, #a0aec0);
}

.dark-theme .notification-close:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.dark-theme .notification-success {
  background-color: rgba(40, 167, 69, 0.2);
}

.dark-theme .notification-success .notification-message {
  color: #68d391;
}

.dark-theme .notification-error {
  background-color: rgba(220, 53, 69, 0.2);
}

.dark-theme .notification-error .notification-message {
  color: #fc8181;
}

.dark-theme .notification-warning {
  background-color: rgba(255, 193, 7, 0.2);
}

.dark-theme .notification-warning .notification-message {
  color: #f6e05e;
}

.dark-theme .notification-info {
  background-color: rgba(23, 162, 184, 0.2);
}

.dark-theme .notification-info .notification-message {
  color: #63b3ed;
}

/* Responsive design */
@media (max-width: 768px) {
  .notification-container {
    left: 20px;
    right: 20px;
    max-width: none;
  }

  .notification {
    transform: translateY(-100%);
  }

  .notification.show {
    transform: translateY(0);
  }

  .notification.hide {
    transform: translateY(-100%);
  }
}

/* Animation for multiple notifications */
.notification:nth-child(n + 4) {
  opacity: 0.8;
  transform: scale(0.95);
}

.notification:nth-child(n + 6) {
  display: none;
}

/* Form validation error styling */
.form-field-error {
  border-color: #dc3545 !important;
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.form-field-success {
  border-color: #28a745 !important;
  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.form-error-message {
  color: #dc3545;
  font-size: 12px;
  margin-top: 4px;
  display: block;
  animation: fadeIn 0.3s ease;
}

.form-group.has-error .form-label {
  color: #dc3545;
}

.form-group.has-success .form-label {
  color: #28a745;
}

/* Fade in animation for error messages */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Loading state */
.notification.loading {
  position: relative;
  overflow: hidden;
}

.notification.loading::after {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 2px;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.6),
    transparent
  );
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}
