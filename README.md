# Kashier Desktop

A comprehensive desktop cashier application built with Electron.js for small businesses and restaurants.

## Features

### 🛒 **Order Management**

- Create and manage customer orders
- Add multiple items with quantities and prices
- Calculate subtotals, tax, and discounts automatically
- Support for different payment methods (Cash, Card, Other)
- Order status tracking (Pending, Completed, Cancelled)

### 👥 **Customer Management**

- Add, edit, and delete customers
- Store customer information (Name, Phone, Email, Address)
- **Egyptian Phone Number Support** with validation and formatting
- Search customers by name, phone, or email
- View customer order history and statistics
- Prevent deletion of customers with existing orders
- **Real-time form validation** with instant feedback

### 📊 **Dashboard & Analytics**

- Daily sales overview
- Order statistics and trends
- Customer analytics
- Best-selling items tracking
- Customizable date range filtering

### 🖨️ **Receipt & Export Features**

- Print professional receipts for orders
- Export orders to CSV format
- Export customer data
- Backup and restore functionality

### 🎨 **User Interface**

- Clean, modern cashier-style interface
- Dark/Light theme toggle with system preference detection
- Responsive design for different screen sizes
- Keyboard shortcuts for quick navigation
- **Enhanced notification system** with toast notifications
- **Real-time form validation** with visual feedback
- **Egyptian phone number formatting** as you type

### 💾 **Data Storage**

- Local NeDB database for offline operation
- Persistent data storage between sessions
- Automatic data backup capabilities
- No internet connection required

## 🆕 Recent Improvements

### ✅ Egyptian Phone Number Support

- **Mobile Numbers**: 01xxxxxxxxx (11 digits) - supports all Egyptian carriers (Vodafone, Orange, Etisalat, WE)
- **Landline Numbers**: 0xxxxxxxx (9-10 digits) - supports all Egyptian area codes
- **Real-time Formatting**: Automatic formatting as you type (e.g., 012 7999 5486)
- **Validation**: Instant validation with clear, specific error messages
- **Examples**:
  - Mobile: 01279995486 → 012 7999 5486
  - Landline: 0503612247 → ************

### ✅ Enhanced Error Handling & Validation

- **Real-time Validation**: Form fields are validated as you type
- **Clear Error Messages**: Specific error messages for each validation rule
- **Visual Feedback**: Fields show success/error states with colors and borders
- **No Page Reloads**: Errors can be corrected without losing form data
- **Instant Feedback**: Users know immediately if their input is valid

### ✅ Modern Notification System

- **Toast Notifications**: Modern notification system replaces basic alerts
- **Multiple Types**: Success, error, warning, and info notifications
- **Auto-dismiss**: Notifications automatically disappear after a set time
- **Dismissible**: Users can manually close notifications
- **Responsive**: Works well on different screen sizes

### ✅ Improved User Experience

- **Better Formatting**: Phone numbers are automatically formatted for readability
- **Responsive Design**: Enhanced mobile and tablet support
- **Accessibility**: Better keyboard navigation and screen reader support
- **Performance**: Faster form validation and better error handling

## Technology Stack

- **Frontend**: HTML5, CSS3, Vanilla JavaScript
- **Backend**: Node.js (built into Electron)
- **Desktop Framework**: Electron.js
- **Database**: NeDB (embedded NoSQL database)
- **Build Tool**: Electron Builder
- **PDF Generation**: jsPDF
- **Styling**: Custom CSS with CSS Variables for theming

## Installation

### Prerequisites

- Node.js (version 16 or higher)
- npm (comes with Node.js)

### Development Setup

1. **Clone the repository**

   ```bash
   git clone <repository-url>
   cd kashier-desktop
   ```

2. **Install dependencies**

   ```bash
   npm install
   ```

3. **Run in development mode**
   ```bash
   npm run dev
   ```

### Building for Production

1. **Build for Windows**

   ```bash
   npm run build:win
   ```

2. **Create portable executable**

   ```bash
   npm run pack
   ```

3. **Build for distribution**
   ```bash
   npm run dist
   ```

## Project Structure

```
kashier-desktop/
├── src/
│   ├── main.js                 # Main Electron process
│   ├── controllers/            # Business logic controllers
│   │   ├── database.js         # Database operations
│   │   ├── customers.js        # Customer management
│   │   ├── orders.js          # Order management
│   │   └── dashboard.js       # Dashboard analytics
│   ├── models/                # Data models
│   │   ├── Customer.js        # Customer model
│   │   └── Order.js           # Order model
│   ├── utils/                 # Utility functions
│   │   ├── helpers.js         # Common helpers
│   │   └── theme.js           # Theme management
│   └── views/                 # Frontend files
│       ├── index.html         # Main HTML file
│       ├── styles/            # CSS files
│       │   ├── main.css       # Main styles
│       │   ├── components.css # Component styles
│       │   └── themes.css     # Theme definitions
│       └── scripts/           # Frontend JavaScript
│           └── app.js         # Main app logic
├── assets/                    # Static assets
├── dist/                      # Build output
├── package.json              # Project configuration
└── README.md                 # This file
```

## Usage Guide

### Getting Started

1. **Launch the application**

   - Run `npm run dev` for development
   - Or run the built executable

2. **Add your first customer**

   - Navigate to the "Customers" tab
   - Click "Add Customer"
   - Fill in customer details (Name and Phone are required)

3. **Create an order**
   - Go to "New Order" tab
   - Select a customer
   - Add items with quantities and prices
   - Review totals and save the order

### Navigation

- **Dashboard** (Ctrl+1): View sales analytics and recent orders
- **Customers** (Ctrl+2): Manage customer information
- **Orders** (Ctrl+3): View and manage all orders
- **New Order** (Ctrl+N): Create a new order

### Keyboard Shortcuts

- `Ctrl+1` - Dashboard
- `Ctrl+2` - Customers
- `Ctrl+3` - Orders
- `Ctrl+N` - New Order
- `Ctrl+T` - Toggle Theme
- `Ctrl+E` - Export Data
- `Escape` - Close Modal

### Data Management

#### Backup Data

- Use the menu: File → Export Data
- Saves all customers, orders, and settings to a JSON file

#### Restore Data

- Import a previously exported JSON backup file
- **Warning**: This will replace all existing data

#### Export Orders

- Filter orders by date range
- Export to CSV format for external analysis

## Configuration

### Theme Settings

The application supports both light and dark themes:

- Automatic detection of system preference
- Manual toggle via the theme button
- Persistent theme selection

### Database Location

Data is stored in the user's application data directory:

- Windows: `%APPDATA%/kashier-desktop/`
- Files: `customers.db`, `orders.db`, `settings.db`

## Development

### Adding New Features

1. **Database Operations**: Extend `src/controllers/database.js`
2. **Business Logic**: Add controllers in `src/controllers/`
3. **Data Models**: Create models in `src/models/`
4. **UI Components**: Update `src/views/` files
5. **Styling**: Modify CSS files in `src/views/styles/`

### Code Structure Guidelines

- Use ES6+ features
- Follow the MVC pattern
- Implement proper error handling
- Add JSDoc comments for functions
- Use semantic HTML and accessible design

### Testing

The application includes comprehensive testing features:

- **Form validation** with real-time feedback
- **Error handling** with user-friendly messages
- **Data persistence** testing
- **Phone number validation** testing

#### Testing Phone Number Validation

A dedicated test file (`test-phone-validation.html`) is included to verify Egyptian phone number validation:

1. Open `test-phone-validation.html` in a browser
2. Test various phone number formats
3. Verify real-time validation and formatting
4. Check test cases for edge cases

#### Manual Testing Checklist

1. **Customer Management**:

   - Add customer with Egyptian mobile number (01xxxxxxxxx)
   - Add customer with Egyptian landline (0xxxxxxxx)
   - Test real-time validation and formatting
   - Verify error messages for invalid numbers

2. **Form Validation**:

   - Test required field validation
   - Test email format validation
   - Test phone number validation
   - Verify visual feedback (red/green borders)

3. **Notifications**:

   - Test success notifications
   - Test error notifications
   - Test notification auto-dismiss
   - Test manual notification dismissal

4. **General Testing**:
   - Run in development mode
   - Test all CRUD operations
   - Verify data persistence
   - Test export/import functionality

## Troubleshooting

### Common Issues

1. **Application won't start**

   - Ensure Node.js is installed
   - Run `npm install` to install dependencies
   - Check for error messages in the console

2. **Data not saving**

   - Check file permissions in the data directory
   - Ensure sufficient disk space
   - Restart the application

3. **Build fails**
   - Clear node_modules and reinstall: `rm -rf node_modules && npm install`
   - Check for missing dependencies
   - Ensure proper build configuration

### Performance Tips

- Regularly backup your data
- Keep the number of orders reasonable (< 10,000 for optimal performance)
- Use date filters when viewing large order lists
- Clear browser cache if running in development mode

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:

- Check the troubleshooting section
- Review the code documentation
- Create an issue in the repository

## Roadmap

Future enhancements may include:

- Inventory management
- Multiple payment processing
- Advanced reporting
- Multi-user support
- Cloud synchronization
- Barcode scanning
- Integration with accounting software

---

**Built with ❤️ using Electron.js**
