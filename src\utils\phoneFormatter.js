/**
 * Egyptian Phone Number Formatter
 * Provides real-time formatting for Egyptian phone numbers
 */

class PhoneFormatter {
  constructor() {
    this.init();
  }

  /**
   * Initialize phone formatting
   */
  init() {
    // Add event listeners for phone input fields
    document.addEventListener('input', this.handlePhoneInput.bind(this));
    document.addEventListener('keydown', this.handleKeyDown.bind(this));
  }

  /**
   * Handle phone input formatting
   */
  handlePhoneInput(event) {
    const field = event.target;
    
    // Check if this is a phone field
    if (field.type === 'tel' || field.classList.contains('phone-input') || field.id.includes('phone')) {
      this.formatPhoneField(field);
    }
  }

  /**
   * Handle keydown events for phone fields
   */
  handleKeyDown(event) {
    const field = event.target;
    
    // Check if this is a phone field
    if (field.type === 'tel' || field.classList.contains('phone-input') || field.id.includes('phone')) {
      // Allow: backspace, delete, tab, escape, enter
      if ([8, 9, 27, 13, 46].indexOf(event.keyCode) !== -1 ||
          // Allow: Ctrl+A, Ctrl+C, Ctrl+V, Ctrl+X
          (event.keyCode === 65 && event.ctrlKey === true) ||
          (event.keyCode === 67 && event.ctrlKey === true) ||
          (event.keyCode === 86 && event.ctrlKey === true) ||
          (event.keyCode === 88 && event.ctrlKey === true) ||
          // Allow: home, end, left, right
          (event.keyCode >= 35 && event.keyCode <= 39)) {
        return;
      }
      
      // Ensure that it is a number and stop the keypress
      if ((event.shiftKey || (event.keyCode < 48 || event.keyCode > 57)) && (event.keyCode < 96 || event.keyCode > 105)) {
        event.preventDefault();
      }
    }
  }

  /**
   * Format phone field in real-time
   */
  formatPhoneField(field) {
    const cursorPosition = field.selectionStart;
    const value = field.value;
    const cleanValue = this.cleanPhoneNumber(value);
    const formattedValue = this.formatEgyptianPhone(cleanValue);
    
    // Only update if the value actually changed
    if (field.value !== formattedValue) {
      field.value = formattedValue;
      
      // Restore cursor position
      const newCursorPosition = this.calculateNewCursorPosition(value, formattedValue, cursorPosition);
      field.setSelectionRange(newCursorPosition, newCursorPosition);
    }
  }

  /**
   * Clean phone number (remove all non-digits)
   */
  cleanPhoneNumber(phone) {
    return phone.replace(/\D/g, '');
  }

  /**
   * Format Egyptian phone number
   */
  formatEgyptianPhone(cleanPhone) {
    // Don't format if empty
    if (!cleanPhone) return '';
    
    // Mobile numbers: 01xxxxxxxxx
    if (cleanPhone.startsWith('01')) {
      if (cleanPhone.length <= 3) {
        return cleanPhone;
      } else if (cleanPhone.length <= 7) {
        return `${cleanPhone.slice(0, 3)} ${cleanPhone.slice(3)}`;
      } else if (cleanPhone.length <= 11) {
        return `${cleanPhone.slice(0, 3)} ${cleanPhone.slice(3, 7)} ${cleanPhone.slice(7)}`;
      } else {
        // Limit to 11 digits for mobile
        return `${cleanPhone.slice(0, 3)} ${cleanPhone.slice(3, 7)} ${cleanPhone.slice(7, 11)}`;
      }
    }
    
    // Landline numbers: 0xxxxxxxx
    if (cleanPhone.startsWith('0') && !cleanPhone.startsWith('01')) {
      if (cleanPhone.length <= 3) {
        return cleanPhone;
      } else if (cleanPhone.length <= 6) {
        return `${cleanPhone.slice(0, 3)} ${cleanPhone.slice(3)}`;
      } else if (cleanPhone.length <= 9) {
        return `${cleanPhone.slice(0, 3)} ${cleanPhone.slice(3, 6)} ${cleanPhone.slice(6)}`;
      } else if (cleanPhone.length <= 10) {
        return `${cleanPhone.slice(0, 3)} ${cleanPhone.slice(3, 7)} ${cleanPhone.slice(7)}`;
      } else {
        // Limit to 10 digits for landline
        return `${cleanPhone.slice(0, 3)} ${cleanPhone.slice(3, 7)} ${cleanPhone.slice(7, 10)}`;
      }
    }
    
    // For other patterns, just return the clean number
    return cleanPhone;
  }

  /**
   * Calculate new cursor position after formatting
   */
  calculateNewCursorPosition(oldValue, newValue, oldPosition) {
    // Count spaces before cursor in old value
    const spacesBefore = (oldValue.slice(0, oldPosition).match(/ /g) || []).length;
    
    // Count spaces before cursor in new value
    let newPosition = oldPosition;
    let spacesInNew = 0;
    
    for (let i = 0; i < newValue.length && spacesInNew <= spacesBefore; i++) {
      if (newValue[i] === ' ') {
        spacesInNew++;
        if (spacesInNew > spacesBefore) {
          newPosition = i;
          break;
        }
      }
      newPosition = i + 1;
    }
    
    return Math.min(newPosition, newValue.length);
  }

  /**
   * Get unformatted phone number
   */
  getUnformattedPhone(formattedPhone) {
    return this.cleanPhoneNumber(formattedPhone);
  }

  /**
   * Validate Egyptian phone number
   */
  isValidEgyptianPhone(phone) {
    const cleanPhone = this.cleanPhoneNumber(phone);
    
    // Egyptian mobile numbers: 01xxxxxxxxx (11 digits total)
    const mobilePattern = /^01[0-9]{9}$/;
    if (mobilePattern.test(cleanPhone)) {
      return { isValid: true, type: 'mobile' };
    }
    
    // Egyptian landline numbers: 0xxxxxxxx (9-10 digits total)
    const landlinePattern = /^0[2-9][0-9]{7,8}$/;
    if (landlinePattern.test(cleanPhone)) {
      return { isValid: true, type: 'landline' };
    }
    
    return { isValid: false, type: null };
  }

  /**
   * Get phone number type and validation info
   */
  getPhoneInfo(phone) {
    const cleanPhone = this.cleanPhoneNumber(phone);
    const validation = this.isValidEgyptianPhone(phone);
    
    return {
      clean: cleanPhone,
      formatted: this.formatEgyptianPhone(cleanPhone),
      isValid: validation.isValid,
      type: validation.type,
      length: cleanPhone.length
    };
  }

  /**
   * Add placeholder text based on phone type
   */
  updatePlaceholder(field) {
    const value = this.cleanPhoneNumber(field.value);
    
    if (value.startsWith('01')) {
      field.placeholder = '01x xxxx xxxx';
    } else if (value.startsWith('0')) {
      field.placeholder = '0xx xxx xxxx';
    } else {
      field.placeholder = 'Enter Egyptian phone number';
    }
  }
}

// Create global instance
const phoneFormatter = new PhoneFormatter();

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = PhoneFormatter;
} else {
  window.PhoneFormatter = PhoneFormatter;
  window.phoneFormatter = phoneFormatter;
}
