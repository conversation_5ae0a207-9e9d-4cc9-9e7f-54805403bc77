/**
 * Main Application Script
 * Handles navigation, initialization, and global app functionality
 */
class KashierApp {
  constructor() {
    this.currentView = "dashboard";
    this.controllers = {};
    this.isInitialized = false;

    this.initializeApp();
  }

  /**
   * Initialize the application
   */
  async initializeApp() {
    try {
      console.log("Initializing Kashier Desktop...");

      // Wait for DOM to be ready
      if (document.readyState === "loading") {
        document.addEventListener("DOMContentLoaded", () => this.init());
      } else {
        await this.init();
      }
    } catch (error) {
      console.error("Error initializing app:", error);
      this.showAlert("Failed to initialize application", "error");
    }
  }

  /**
   * Main initialization method
   */
  async init() {
    try {
      // Initialize navigation
      this.initializeNavigation();

      // Initialize menu listeners
      this.initializeMenuListeners();

      // Initialize keyboard shortcuts
      this.initializeKeyboardShortcuts();

      // Set initial view
      this.showView(this.currentView);

      // Mark as initialized
      this.isInitialized = true;

      console.log("Kashier Desktop initialized successfully");
    } catch (error) {
      console.error("Error during initialization:", error);
      throw error;
    }
  }

  /**
   * Initialize navigation system
   */
  initializeNavigation() {
    const navButtons = document.querySelectorAll(".nav-btn");
    console.log("Found navigation buttons:", navButtons.length);

    navButtons.forEach((button) => {
      button.addEventListener("click", (e) => {
        e.preventDefault();
        const view = button.getAttribute("data-view");
        console.log("Navigation button clicked:", view);
        if (view) {
          this.showView(view);
        }
      });
    });
  }

  /**
   * Initialize menu listeners for Electron menu integration
   */
  initializeMenuListeners() {
    if (typeof require !== "undefined") {
      const { ipcRenderer } = require("electron");

      // Menu navigation
      ipcRenderer.on("menu-navigate", (event, view) => {
        this.showView(view);
      });

      // Menu new order
      ipcRenderer.on("menu-new-order", () => {
        this.showView("new-order");
      });

      // Menu export data
      ipcRenderer.on("menu-export-data", () => {
        this.exportAllData();
      });

      // Menu about
      ipcRenderer.on("menu-about", () => {
        this.showAboutDialog();
      });
    }
  }

  /**
   * Initialize keyboard shortcuts
   */
  initializeKeyboardShortcuts() {
    document.addEventListener("keydown", (e) => {
      // Ctrl/Cmd + Number keys for navigation
      if ((e.ctrlKey || e.metaKey) && !e.shiftKey && !e.altKey) {
        switch (e.key) {
          case "1":
            e.preventDefault();
            this.showView("dashboard");
            break;
          case "2":
            e.preventDefault();
            this.showView("customers");
            break;
          case "3":
            e.preventDefault();
            this.showView("orders");
            break;
          case "n":
            e.preventDefault();
            this.showView("new-order");
            break;
          case "t":
            e.preventDefault();
            if (window.themeManager) {
              window.themeManager.toggleTheme();
            }
            break;
        }
      }

      // Escape key to close modals
      if (e.key === "Escape") {
        this.closeModal();
      }
    });
  }

  /**
   * Show specific view
   */
  showView(viewName) {
    try {
      console.log("Showing view:", viewName);

      // Update navigation
      this.updateNavigation(viewName);

      // Hide all views
      const views = document.querySelectorAll(".view");
      console.log("Found views:", views.length);
      views.forEach((view) => view.classList.remove("active"));

      // Show target view
      const targetView = document.getElementById(`${viewName}-view`);
      console.log("Target view element:", targetView);
      if (targetView) {
        targetView.classList.add("active");
        this.currentView = viewName;
        console.log("View activated:", viewName);

        // Trigger view-specific initialization
        this.onViewChanged(viewName);
      } else {
        console.warn(`View not found: ${viewName}`);
      }
    } catch (error) {
      console.error("Error showing view:", error);
    }
  }

  /**
   * Update navigation active state
   */
  updateNavigation(activeView) {
    const navButtons = document.querySelectorAll(".nav-btn");

    navButtons.forEach((button) => {
      const view = button.getAttribute("data-view");
      if (view === activeView) {
        button.classList.add("active");
      } else {
        button.classList.remove("active");
      }
    });
  }

  /**
   * Handle view change events
   */
  onViewChanged(viewName) {
    console.log("View changed to:", viewName);

    // Trigger refresh for specific views
    switch (viewName) {
      case "dashboard":
        console.log(
          "Dashboard controller available:",
          !!window.dashboardController
        );
        if (window.dashboardController) {
          window.dashboardController.loadDashboardData();
        }
        break;
      case "customers":
        console.log(
          "Customer controller available:",
          !!window.customerController
        );
        if (window.customerController) {
          window.customerController.loadCustomers();
        }
        break;
      case "orders":
        console.log("Order controller available:", !!window.orderController);
        if (window.orderController) {
          window.orderController.loadOrders();
        }
        break;
      case "new-order":
        console.log(
          "Order controller available for new order:",
          !!window.orderController
        );
        if (window.orderController) {
          window.orderController.initializeNewOrder();
        } else {
          // If orderController isn't ready, try again after a short delay
          setTimeout(() => {
            if (window.orderController) {
              window.orderController.initializeNewOrder();
            }
          }, 100);
        }
        break;
    }
  }

  /**
   * Close any open modals
   */
  closeModal() {
    const modalOverlay = document.getElementById("modal-overlay");
    if (modalOverlay && modalOverlay.classList.contains("active")) {
      modalOverlay.classList.remove("active");
    }
  }

  /**
   * Export all application data
   */
  async exportAllData() {
    try {
      const exportData = {
        customers: await dbController.find("customers"),
        orders: await dbController.find("orders"),
        settings: await dbController.find("settings"),
        exportDate: new Date().toISOString(),
        version: "1.0.0",
      };

      const jsonContent = JSON.stringify(exportData, null, 2);
      const filename = `kashier-backup-${
        new Date().toISOString().split("T")[0]
      }.json`;

      this.downloadFile(jsonContent, filename, "application/json");
      this.showAlert("Data exported successfully", "success");
    } catch (error) {
      console.error("Error exporting data:", error);
      this.showAlert("Error exporting data", "error");
    }
  }

  /**
   * Import application data
   */
  async importData(file) {
    try {
      const content = await this.readFile(file);
      const data = JSON.parse(content);

      // Validate data structure
      if (!data.customers || !data.orders) {
        throw new Error("Invalid backup file format");
      }

      // Confirm import
      if (!confirm("This will replace all existing data. Are you sure?")) {
        return;
      }

      // Restore data
      await dbController.restore(data);

      // Refresh all views
      this.refreshAllViews();

      this.showAlert("Data imported successfully", "success");
    } catch (error) {
      console.error("Error importing data:", error);
      this.showAlert("Error importing data: " + error.message, "error");
    }
  }

  /**
   * Refresh all views
   */
  refreshAllViews() {
    if (window.dashboardController) {
      window.dashboardController.loadDashboardData();
    }
    if (window.customerController) {
      window.customerController.loadCustomers();
    }
    if (window.orderController) {
      window.orderController.loadOrders();
    }
  }

  /**
   * Show about dialog
   */
  showAboutDialog() {
    const modalTitle = document.getElementById("modal-title");
    const modalBody = document.getElementById("modal-body");

    if (modalTitle) modalTitle.textContent = "About Kashier Desktop";

    if (modalBody) {
      modalBody.innerHTML = `
                <div class="about-dialog">
                    <div class="text-center mb-4">
                        <h2>Kashier Desktop</h2>
                        <p class="text-muted">Version 1.0.0</p>
                    </div>
                    
                    <p>A comprehensive desktop cashier application built with Electron.js for small businesses and restaurants.</p>
                    
                    <h4>Features:</h4>
                    <ul>
                        <li>Customer Management</li>
                        <li>Order Processing</li>
                        <li>Sales Analytics</li>
                        <li>Receipt Printing</li>
                        <li>Data Export/Import</li>
                        <li>Dark/Light Theme</li>
                    </ul>
                    
                    <h4>Technology Stack:</h4>
                    <ul>
                        <li>Electron.js</li>
                        <li>Node.js</li>
                        <li>NeDB Database</li>
                        <li>HTML/CSS/JavaScript</li>
                    </ul>
                    
                    <div class="text-center mt-4">
                        <button type="button" class="btn btn-primary" onclick="app.closeModal()">
                            Close
                        </button>
                    </div>
                </div>
            `;
    }

    const modalOverlay = document.getElementById("modal-overlay");
    if (modalOverlay) {
      modalOverlay.classList.add("active");
    }
  }

  /**
   * Utility methods
   */
  downloadFile(content, filename, contentType = "text/plain") {
    const blob = new Blob([content], { type: contentType });
    const link = document.createElement("a");

    if (link.download !== undefined) {
      const url = URL.createObjectURL(blob);
      link.setAttribute("href", url);
      link.setAttribute("download", filename);
      link.style.visibility = "hidden";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    }
  }

  readFile(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => resolve(e.target.result);
      reader.onerror = (e) => reject(e);
      reader.readAsText(file);
    });
  }

  showAlert(message, type = "info") {
    // Use the enhanced notification system
    if (window.notifications) {
      switch (type) {
        case "error":
          window.notifications.error(message);
          break;
        case "success":
          window.notifications.success(message);
          break;
        case "warning":
          window.notifications.warning(message);
          break;
        default:
          window.notifications.info(message);
      }
    } else {
      // Fallback to simple alert
      if (type === "error") {
        alert("Error: " + message);
      } else if (type === "success") {
        alert("Success: " + message);
      } else {
        alert(message);
      }
    }
  }

  /**
   * Get current view
   */
  getCurrentView() {
    return this.currentView;
  }

  /**
   * Check if app is initialized
   */
  isReady() {
    return this.isInitialized;
  }
}

// Initialize the application
const app = new KashierApp();

// Make app globally available
window.app = app;

// Export for use in other modules
if (typeof module !== "undefined" && module.exports) {
  module.exports = KashierApp;
}
