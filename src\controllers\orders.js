/**
 * Order Controller
 * Handles all order-related operations and UI interactions
 */
class OrderController {
  constructor() {
    console.log("Initializing Order Controller...");
    this.orders = [];
    this.currentOrder = null;
    this.currentOrderItems = [];
    this.isLoading = false;
    this.taxRate = 0; // Can be configured

    this.initializeEventListeners();
    this.loadOrders();
  }

  /**
   * Initialize event listeners for order management
   */
  initializeEventListeners() {
    // Filter orders button
    const filterOrdersBtn = document.getElementById("filter-orders");
    if (filterOrdersBtn) {
      filterOrdersBtn.addEventListener("click", () => this.filterOrders());
    }

    // Export orders button
    const exportOrdersBtn = document.getElementById("export-orders");
    if (exportOrdersBtn) {
      exportOrdersBtn.addEventListener("click", () => this.exportOrders());
    }

    // Save order button
    const saveOrderBtn = document.getElementById("save-order");
    if (saveOrderBtn) {
      saveOrderBtn.addEventListener("click", () => this.saveCurrentOrder());
    }

    // Print receipt button
    const printReceiptBtn = document.getElementById("print-receipt");
    if (printReceiptBtn) {
      printReceiptBtn.addEventListener("click", () => this.printReceipt());
    }
  }

  /**
   * Load all orders from database
   */
  async loadOrders() {
    try {
      this.setLoading(true);
      this.orders = await dbController.find(
        "orders",
        {},
        Order.getDefaultSort()
      );
      this.displayOrders();
    } catch (error) {
      console.error("Error loading orders:", error);
      this.showAlert("Error loading orders", "error");
    } finally {
      this.setLoading(false);
    }
  }

  /**
   * Display orders in the UI
   */
  displayOrders(ordersToShow = null) {
    const ordersList = document.getElementById("orders-list");
    if (!ordersList) return;

    const orders = ordersToShow || this.orders;

    if (orders.length === 0) {
      ordersList.innerHTML = `
                <div class="text-center" style="padding: 40px;">
                    <p class="text-muted">No orders found.</p>
                </div>
            `;
      return;
    }

    ordersList.innerHTML = orders
      .map(
        (order) => `
            <div class="list-item" data-order-id="${order._id}">
                <div class="item-info">
                    <h4>Order #${this.escapeHtml(order.orderNumber)}</h4>
                    <p>
                        👤 ${this.escapeHtml(order.customerName)} • 
                        📅 ${new Date(order.orderDate).toLocaleDateString()} • 
                        💰 $${order.total.toFixed(2)}
                    </p>
                    <p>
                        <span class="status-badge status-${
                          order.status
                        }">${this.getStatusDisplay(order.status)}</span>
                        • ${this.getPaymentMethodDisplay(order.paymentMethod)}
                        • ${order.items.length} item(s)
                    </p>
                </div>
                <div class="item-actions">
                    <button class="btn btn-small btn-secondary" onclick="orderController.viewOrder('${
                      order._id
                    }')">
                        View
                    </button>
                    ${
                      order.status === "pending"
                        ? `
                        <button class="btn btn-small btn-primary" onclick="orderController.editOrder('${order._id}')">
                            Edit
                        </button>
                    `
                        : ""
                    }
                    <button class="btn btn-small btn-success" onclick="orderController.printOrderReceipt('${
                      order._id
                    }')">
                        Print
                    </button>
                </div>
            </div>
        `
      )
      .join("");
  }

  /**
   * Filter orders by date range
   */
  async filterOrders() {
    try {
      const dateFrom = document.getElementById("orders-date-from").value;
      const dateTo = document.getElementById("orders-date-to").value;

      let filteredOrders = this.orders;

      if (dateFrom || dateTo) {
        filteredOrders = this.orders.filter((order) => {
          const orderDate = new Date(order.orderDate);

          if (dateFrom && orderDate < new Date(dateFrom)) {
            return false;
          }

          if (dateTo) {
            const endDate = new Date(dateTo);
            endDate.setHours(23, 59, 59, 999);
            if (orderDate > endDate) {
              return false;
            }
          }

          return true;
        });
      }

      this.displayOrders(filteredOrders);
    } catch (error) {
      console.error("Error filtering orders:", error);
      this.showAlert("Error filtering orders", "error");
    }
  }

  /**
   * Create new order for specific customer
   */
  createOrderForCustomer(customerId) {
    const customer = customerController.getCustomerById(customerId);
    if (!customer) {
      this.showAlert("Customer not found", "error");
      return;
    }

    this.currentOrder = new Order({
      customerId: customer._id,
      customerName: customer.name,
    });

    this.currentOrderItems = [];
    this.showNewOrderView();
    this.displayNewOrderForm();
  }

  /**
   * Show new order view
   */
  showNewOrderView() {
    // Switch to new order tab
    const navBtns = document.querySelectorAll(".nav-btn");
    navBtns.forEach((btn) => btn.classList.remove("active"));

    const newOrderBtn = document.querySelector('[data-view="new-order"]');
    if (newOrderBtn) {
      newOrderBtn.classList.add("active");
    }

    // Show new order view
    const views = document.querySelectorAll(".view");
    views.forEach((view) => view.classList.remove("active"));

    const newOrderView = document.getElementById("new-order-view");
    if (newOrderView) {
      newOrderView.classList.add("active");
    }
  }

  /**
   * Display new order form
   */
  displayNewOrderForm() {
    const orderForm = document.querySelector(".order-form");
    if (!orderForm) return;

    // Get customers safely
    const customers = this.getAvailableCustomers();

    orderForm.innerHTML = `
            <div class="order-form-container">
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">Customer *</label>
                        <select id="order-customer" class="form-input" required>
                            <option value="">Select a customer</option>
                            <option value="walk-in">Walk-in Customer</option>
                            ${customers
                              .map(
                                (customer) => `
                                <option value="${customer.id}" ${
                                  this.currentOrder?.customerId === customer.id
                                    ? "selected"
                                    : ""
                                }>
                                    ${this.escapeHtml(customer.displayName)}
                                </option>
                            `
                              )
                              .join("")}
                        </select>
                        ${
                          customers.length === 0
                            ? '<p class="text-muted">No customers available. You can use "Walk-in Customer" or <a href="#" onclick="app.showView(\'customers\')">add a customer first</a>.</p>'
                            : ""
                        }
                    </div>
                    <div class="form-group">
                        <label class="form-label">Payment Method</label>
                        <select id="order-payment-method" class="form-input">
                            <option value="cash">Cash</option>
                            <option value="card">Card</option>
                            <option value="other">Other</option>
                        </select>
                    </div>
                </div>

                <div class="order-items-section">
                    <div class="section-header">
                        <h3>Order Items</h3>
                        <button type="button" class="btn btn-primary" onclick="orderController.addOrderItem()">
                            Add Item
                        </button>
                    </div>
                    
                    <div id="order-items-list" class="order-items-list">
                        ${this.renderOrderItems()}
                    </div>
                </div>

                <div class="order-totals">
                    <div class="totals-row">
                        <span>Subtotal:</span>
                        <span id="order-subtotal">$0.00</span>
                    </div>
                    <div class="totals-row">
                        <span>Tax (${this.taxRate}%):</span>
                        <span id="order-tax">$0.00</span>
                    </div>
                    <div class="totals-row">
                        <label>Discount:</label>
                        <input type="number" id="order-discount" class="form-input" value="0" min="0" step="0.01" 
                               onchange="orderController.calculateTotals()">
                    </div>
                    <div class="totals-row total-row">
                        <strong>Total: <span id="order-total">$0.00</span></strong>
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">Notes</label>
                    <textarea id="order-notes" class="form-input" rows="3" 
                              placeholder="Additional notes for this order">${
                                this.currentOrder?.notes || ""
                              }</textarea>
                </div>
            </div>
        `;

    // Add event listeners
    const customerSelect = document.getElementById("order-customer");
    if (customerSelect) {
      customerSelect.addEventListener("change", (e) => {
        if (this.currentOrder) {
          if (e.target.value === "walk-in") {
            this.currentOrder.customerId = "walk-in";
            this.currentOrder.customerName = "Walk-in Customer";
          } else if (e.target.value) {
            const customer = this.getCustomerById(e.target.value);
            if (customer) {
              this.currentOrder.customerId = customer._id;
              this.currentOrder.customerName = customer.name;
            }
          }
        }
      });
    }

    this.calculateTotals();
  }

  /**
   * Render order items list
   */
  renderOrderItems() {
    if (this.currentOrderItems.length === 0) {
      return '<p class="text-muted text-center">No items added yet. Click "Add Item" to start.</p>';
    }

    return this.currentOrderItems
      .map(
        (item, index) => `
            <div class="order-item" data-item-index="${index}">
                <div class="item-details">
                    <div class="form-row three-cols">
                        <input type="text" class="form-input" placeholder="Item name" 
                               value="${this.escapeHtml(item.name)}" 
                               onchange="orderController.updateOrderItem(${index}, 'name', this.value)">
                        <input type="number" class="form-input" placeholder="Quantity" 
                               value="${item.quantity}" min="1" step="1"
                               onchange="orderController.updateOrderItem(${index}, 'quantity', this.value)">
                        <input type="number" class="form-input" placeholder="Price" 
                               value="${item.price}" min="0" step="0.01"
                               onchange="orderController.updateOrderItem(${index}, 'price', this.value)">
                    </div>
                    <div class="item-total">
                        Total: $${(item.quantity * item.price).toFixed(2)}
                    </div>
                </div>
                <button type="button" class="btn btn-small btn-danger" 
                        onclick="orderController.removeOrderItem(${index})">
                    Remove
                </button>
            </div>
        `
      )
      .join("");
  }

  /**
   * Add new order item
   */
  addOrderItem() {
    const newItem = {
      id: Date.now().toString(),
      name: "",
      quantity: 1,
      price: 0,
      description: "",
    };

    this.currentOrderItems.push(newItem);
    this.updateOrderItemsDisplay();
    this.calculateTotals();
  }

  /**
   * Update order item
   */
  updateOrderItem(index, field, value) {
    if (this.currentOrderItems[index]) {
      if (field === "quantity" || field === "price") {
        this.currentOrderItems[index][field] = parseFloat(value) || 0;
      } else {
        this.currentOrderItems[index][field] = value;
      }
      this.updateOrderItemsDisplay();
      this.calculateTotals();
    }
  }

  /**
   * Remove order item
   */
  removeOrderItem(index) {
    this.currentOrderItems.splice(index, 1);
    this.updateOrderItemsDisplay();
    this.calculateTotals();
  }

  /**
   * Update order items display
   */
  updateOrderItemsDisplay() {
    const orderItemsList = document.getElementById("order-items-list");
    if (orderItemsList) {
      orderItemsList.innerHTML = this.renderOrderItems();
    }
  }

  /**
   * Calculate order totals
   */
  calculateTotals() {
    if (!this.currentOrder) return;

    // Update current order items
    this.currentOrder.items = this.currentOrderItems;

    // Get discount value
    const discountInput = document.getElementById("order-discount");
    const discount = discountInput ? parseFloat(discountInput.value) || 0 : 0;
    this.currentOrder.discount = discount;

    // Calculate totals
    this.currentOrder.calculateTotals(this.taxRate);

    // Update display
    this.updateTotalsDisplay();
  }

  /**
   * Update totals display
   */
  updateTotalsDisplay() {
    if (!this.currentOrder) return;

    const subtotalEl = document.getElementById("order-subtotal");
    const taxEl = document.getElementById("order-tax");
    const totalEl = document.getElementById("order-total");

    if (subtotalEl)
      subtotalEl.textContent = `$${this.currentOrder.subtotal.toFixed(2)}`;
    if (taxEl) taxEl.textContent = `$${this.currentOrder.tax.toFixed(2)}`;
    if (totalEl) totalEl.textContent = `$${this.currentOrder.total.toFixed(2)}`;
  }

  /**
   * Save current order
   */
  async saveCurrentOrder() {
    try {
      if (!this.currentOrder) {
        this.showAlert("No order to save", "error");
        return;
      }

      // Get form values
      const customerSelect = document.getElementById("order-customer");
      const paymentMethodSelect = document.getElementById(
        "order-payment-method"
      );
      const notesTextarea = document.getElementById("order-notes");

      if (customerSelect) {
        if (customerSelect.value === "walk-in") {
          this.currentOrder.customerId = "walk-in";
          this.currentOrder.customerName = "Walk-in Customer";
        } else if (customerSelect.value) {
          const customer = this.getCustomerById(customerSelect.value);
          if (!customer) {
            this.showAlert("Please select a valid customer", "error");
            return;
          }
          this.currentOrder.customerId = customer._id;
          this.currentOrder.customerName = customer.name;
        } else {
          this.showAlert("Please select a customer", "error");
          return;
        }
      }

      if (paymentMethodSelect) {
        this.currentOrder.paymentMethod = paymentMethodSelect.value;
      }

      if (notesTextarea) {
        this.currentOrder.notes = notesTextarea.value;
      }

      // Update items and calculate totals
      this.currentOrder.items = this.currentOrderItems;
      this.calculateTotals();

      // Validate order
      const errors = this.currentOrder.validate();
      if (errors.length > 0) {
        this.showAlert(errors.join("<br>"), "error");
        return;
      }

      this.setLoading(true);

      // Save to database
      if (this.currentOrder._id) {
        // Update existing order
        await dbController.update(
          "orders",
          { _id: this.currentOrder._id },
          this.currentOrder.toObject()
        );
        this.showAlert("Order updated successfully", "success");
      } else {
        // Create new order
        this.currentOrder.status = "completed"; // Mark as completed when saved
        const newOrder = await dbController.insert(
          "orders",
          this.currentOrder.toObject()
        );
        this.currentOrder._id = newOrder._id;
        this.showAlert("Order saved successfully", "success");
      }

      // Refresh orders list
      await this.loadOrders();

      // Reset form
      this.resetOrderForm();
    } catch (error) {
      console.error("Error saving order:", error);
      this.showAlert("Error saving order", "error");
    } finally {
      this.setLoading(false);
    }
  }

  /**
   * Reset order form
   */
  resetOrderForm() {
    this.currentOrder = null;
    this.currentOrderItems = [];
    this.displayNewOrderForm();
  }

  /**
   * Export orders to CSV
   */
  async exportOrders() {
    try {
      const dateFrom = document.getElementById("orders-date-from").value;
      const dateTo = document.getElementById("orders-date-to").value;

      let ordersToExport = this.orders;

      // Apply date filter if specified
      if (dateFrom || dateTo) {
        ordersToExport = this.orders.filter((order) => {
          const orderDate = new Date(order.orderDate);

          if (dateFrom && orderDate < new Date(dateFrom)) {
            return false;
          }

          if (dateTo) {
            const endDate = new Date(dateTo);
            endDate.setHours(23, 59, 59, 999);
            if (orderDate > endDate) {
              return false;
            }
          }

          return true;
        });
      }

      // Create CSV content
      let csvContent = "Order Export\n\n";
      csvContent +=
        "Order Number,Customer Name,Order Date,Status,Payment Method,Subtotal,Tax,Discount,Total,Items\n";

      ordersToExport.forEach((order) => {
        const itemsText = order.items
          .map((item) => `${item.name} (${item.quantity}x$${item.price})`)
          .join("; ");
        csvContent += `"${order.orderNumber}","${
          order.customerName
        }","${new Date(order.orderDate).toLocaleDateString()}","${
          order.status
        }","${order.paymentMethod}",${order.subtotal},${order.tax},${
          order.discount
        },${order.total},"${itemsText}"\n`;
      });

      // Download CSV
      const filename = `orders-export-${
        new Date().toISOString().split("T")[0]
      }.csv`;
      this.downloadCSV(csvContent, filename);
      this.showAlert("Orders exported successfully", "success");
    } catch (error) {
      console.error("Error exporting orders:", error);
      this.showAlert("Error exporting orders", "error");
    }
  }

  /**
   * View order details
   */
  async viewOrder(orderId) {
    try {
      const order = this.orders.find((o) => o._id === orderId);
      if (!order) {
        this.showAlert("Order not found", "error");
        return;
      }

      const modalTitle = document.getElementById("modal-title");
      const modalBody = document.getElementById("modal-body");

      if (modalTitle) modalTitle.textContent = `Order #${order.orderNumber}`;

      if (modalBody) {
        modalBody.innerHTML = `
                    <div class="order-details">
                        <div class="form-row">
                            <div><strong>Customer:</strong> ${this.escapeHtml(
                              order.customerName
                            )}</div>
                            <div><strong>Date:</strong> ${new Date(
                              order.orderDate
                            ).toLocaleDateString()}</div>
                        </div>
                        <div class="form-row">
                            <div><strong>Status:</strong> <span class="status-badge status-${
                              order.status
                            }">${this.getStatusDisplay(
          order.status
        )}</span></div>
                            <div><strong>Payment:</strong> ${this.getPaymentMethodDisplay(
                              order.paymentMethod
                            )}</div>
                        </div>

                        <h4 style="margin: 20px 0 10px 0;">Order Items</h4>
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Item</th>
                                    <th>Quantity</th>
                                    <th>Price</th>
                                    <th>Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${order.items
                                  .map(
                                    (item) => `
                                    <tr>
                                        <td>${this.escapeHtml(item.name)}</td>
                                        <td>${item.quantity}</td>
                                        <td>$${item.price.toFixed(2)}</td>
                                        <td>$${(
                                          item.quantity * item.price
                                        ).toFixed(2)}</td>
                                    </tr>
                                `
                                  )
                                  .join("")}
                            </tbody>
                        </table>

                        <div class="order-totals">
                            <div class="totals-row">
                                <span>Subtotal:</span>
                                <span>$${order.subtotal.toFixed(2)}</span>
                            </div>
                            <div class="totals-row">
                                <span>Tax:</span>
                                <span>$${order.tax.toFixed(2)}</span>
                            </div>
                            <div class="totals-row">
                                <span>Discount:</span>
                                <span>$${order.discount.toFixed(2)}</span>
                            </div>
                            <div class="totals-row total-row">
                                <strong>Total: $${order.total.toFixed(
                                  2
                                )}</strong>
                            </div>
                        </div>

                        ${
                          order.notes
                            ? `<div class="form-group"><strong>Notes:</strong> ${this.escapeHtml(
                                order.notes
                              )}</div>`
                            : ""
                        }

                        <div class="flex gap-2 justify-between mt-4">
                            <button type="button" class="btn btn-secondary" onclick="orderController.hideModal()">
                                Close
                            </button>
                            <div class="flex gap-2">
                                ${
                                  order.status === "pending"
                                    ? `
                                    <button type="button" class="btn btn-primary" onclick="orderController.editOrder('${order._id}')">
                                        Edit Order
                                    </button>
                                `
                                    : ""
                                }
                                <button type="button" class="btn btn-success" onclick="orderController.printOrderReceipt('${
                                  order._id
                                }')">
                                    Print Receipt
                                </button>
                            </div>
                        </div>
                    </div>
                `;
      }

      this.showModal();
    } catch (error) {
      console.error("Error viewing order:", error);
      this.showAlert("Error loading order details", "error");
    }
  }

  /**
   * Edit existing order
   */
  async editOrder(orderId) {
    try {
      const order = this.orders.find((o) => o._id === orderId);
      if (!order) {
        this.showAlert("Order not found", "error");
        return;
      }

      if (order.status !== "pending") {
        this.showAlert("Only pending orders can be edited", "error");
        return;
      }

      // Set current order for editing
      this.currentOrder = Order.fromObject(order);
      this.currentOrderItems = [...order.items];

      // Show new order view
      this.showNewOrderView();
      this.displayNewOrderForm();
    } catch (error) {
      console.error("Error editing order:", error);
      this.showAlert("Error loading order for editing", "error");
    }
  }

  /**
   * Get available customers for selection
   */
  getAvailableCustomers() {
    try {
      if (window.customerController && window.customerController.customers) {
        return window.customerController.getCustomersForSelection();
      }
      return [];
    } catch (error) {
      console.error("Error getting customers:", error);
      return [];
    }
  }

  /**
   * Get customer by ID
   */
  getCustomerById(customerId) {
    try {
      if (
        window.customerController &&
        window.customerController.getCustomerById
      ) {
        return window.customerController.getCustomerById(customerId);
      }
      return null;
    } catch (error) {
      console.error("Error getting customer by ID:", error);
      return null;
    }
  }

  /**
   * Initialize new order
   */
  initializeNewOrder() {
    this.currentOrder = new Order();
    this.currentOrderItems = [];
    this.displayNewOrderForm();
  }

  /**
   * Utility methods
   */
  getStatusDisplay(status) {
    const statusMap = {
      pending: "Pending",
      completed: "Completed",
      cancelled: "Cancelled",
    };
    return statusMap[status] || status;
  }

  getPaymentMethodDisplay(method) {
    const methodMap = {
      cash: "Cash",
      card: "Card",
      other: "Other",
    };
    return methodMap[method] || method;
  }

  downloadCSV(content, filename) {
    const blob = new Blob([content], { type: "text/csv;charset=utf-8;" });
    const link = document.createElement("a");

    if (link.download !== undefined) {
      const url = URL.createObjectURL(blob);
      link.setAttribute("href", url);
      link.setAttribute("download", filename);
      link.style.visibility = "hidden";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  }

  showModal() {
    const modalOverlay = document.getElementById("modal-overlay");
    if (modalOverlay) {
      modalOverlay.classList.add("active");
    }
  }

  hideModal() {
    const modalOverlay = document.getElementById("modal-overlay");
    if (modalOverlay) {
      modalOverlay.classList.remove("active");
    }
  }

  setLoading(loading) {
    this.isLoading = loading;
  }

  showAlert(message, type = "info") {
    if (type === "error") {
      alert("Error: " + message);
    } else if (type === "success") {
      alert("Success: " + message);
    } else {
      alert(message);
    }
  }

  escapeHtml(text) {
    const div = document.createElement("div");
    div.textContent = text;
    return div.innerHTML;
  }

  /**
   * Print order receipt
   */
  async printOrderReceipt(orderId) {
    try {
      const order = this.orders.find((o) => o._id === orderId);
      if (!order) {
        this.showAlert("Order not found", "error");
        return;
      }

      // Create receipt content
      const receiptContent = this.generateReceiptHTML(order);

      // Open print window
      const printWindow = window.open("", "_blank");
      printWindow.document.write(receiptContent);
      printWindow.document.close();
      printWindow.print();
    } catch (error) {
      console.error("Error printing receipt:", error);
      this.showAlert("Error printing receipt", "error");
    }
  }

  /**
   * Generate receipt HTML
   */
  generateReceiptHTML(order) {
    return `
            <!DOCTYPE html>
            <html>
            <head>
                <title>Receipt - Order #${order.orderNumber}</title>
                <style>
                    body { font-family: Arial, sans-serif; max-width: 400px; margin: 0 auto; padding: 20px; }
                    .header { text-align: center; margin-bottom: 20px; }
                    .order-info { margin-bottom: 20px; }
                    .items-table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
                    .items-table th, .items-table td { padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }
                    .totals { margin-top: 20px; }
                    .total-row { font-weight: bold; font-size: 16px; }
                    .footer { text-align: center; margin-top: 30px; font-size: 12px; }
                </style>
            </head>
            <body>
                <div class="header">
                    <h2>Kashier Desktop</h2>
                    <p>Receipt</p>
                </div>

                <div class="order-info">
                    <p><strong>Order #:</strong> ${order.orderNumber}</p>
                    <p><strong>Customer:</strong> ${order.customerName}</p>
                    <p><strong>Date:</strong> ${new Date(
                      order.orderDate
                    ).toLocaleString()}</p>
                    <p><strong>Payment:</strong> ${this.getPaymentMethodDisplay(
                      order.paymentMethod
                    )}</p>
                </div>

                <table class="items-table">
                    <thead>
                        <tr>
                            <th>Item</th>
                            <th>Qty</th>
                            <th>Price</th>
                            <th>Total</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${order.items
                          .map(
                            (item) => `
                            <tr>
                                <td>${item.name}</td>
                                <td>${item.quantity}</td>
                                <td>$${item.price.toFixed(2)}</td>
                                <td>$${(item.quantity * item.price).toFixed(
                                  2
                                )}</td>
                            </tr>
                        `
                          )
                          .join("")}
                    </tbody>
                </table>

                <div class="totals">
                    <p>Subtotal: $${order.subtotal.toFixed(2)}</p>
                    <p>Tax: $${order.tax.toFixed(2)}</p>
                    <p>Discount: $${order.discount.toFixed(2)}</p>
                    <p class="total-row">Total: $${order.total.toFixed(2)}</p>
                </div>

                ${
                  order.notes
                    ? `<p><strong>Notes:</strong> ${order.notes}</p>`
                    : ""
                }

                <div class="footer">
                    <p>Thank you for your business!</p>
                    <p>Generated on ${new Date().toLocaleString()}</p>
                </div>
            </body>
            </html>
        `;
  }

  /**
   * Print current order receipt
   */
  printReceipt() {
    if (!this.currentOrder || !this.currentOrder._id) {
      this.showAlert("Please save the order first", "error");
      return;
    }

    this.printOrderReceipt(this.currentOrder._id);
  }
}

// Initialize order controller when DOM is loaded
let orderController;
if (typeof document !== "undefined") {
  document.addEventListener("DOMContentLoaded", () => {
    // Wait a bit for other controllers to initialize
    setTimeout(() => {
      orderController = new OrderController();
      window.orderController = orderController;
    }, 100);
  });
}

// Export for use in other modules
if (typeof module !== "undefined" && module.exports) {
  module.exports = OrderController;
} else {
  window.OrderController = OrderController;
}
