/**
 * Real-time Validation System
 * Provides instant feedback for form fields
 */

class ValidationManager {
  constructor() {
    this.validators = new Map();
    this.fieldErrors = new Map();
    this.init();
  }

  /**
   * Initialize validation system
   */
  init() {
    // Add global event listeners for form validation
    document.addEventListener('input', this.handleInput.bind(this));
    document.addEventListener('blur', this.handleBlur.bind(this));
    document.addEventListener('focus', this.handleFocus.bind(this));
  }

  /**
   * Register a field for validation
   * @param {string} fieldId - The ID of the field
   * @param {Array} rules - Array of validation rules
   */
  registerField(fieldId, rules) {
    this.validators.set(fieldId, rules);
  }

  /**
   * Handle input events for real-time validation
   */
  handleInput(event) {
    const field = event.target;
    if (field.classList.contains('validate-realtime')) {
      this.validateField(field, false); // Don't show success on input, only errors
    }
  }

  /**
   * Handle blur events for validation
   */
  handleBlur(event) {
    const field = event.target;
    if (field.classList.contains('validate-on-blur') || field.classList.contains('validate-realtime')) {
      this.validateField(field, true); // Show both success and errors on blur
    }
  }

  /**
   * Handle focus events to clear errors
   */
  handleFocus(event) {
    const field = event.target;
    if (field.classList.contains('validate-realtime') || field.classList.contains('validate-on-blur')) {
      this.clearFieldError(field);
    }
  }

  /**
   * Validate a specific field
   * @param {HTMLElement} field - The field element
   * @param {boolean} showSuccess - Whether to show success state
   */
  validateField(field, showSuccess = false) {
    const fieldId = field.id;
    const rules = this.validators.get(fieldId);
    
    if (!rules) return true;

    const value = field.value.trim();
    const errors = [];

    // Run validation rules
    for (const rule of rules) {
      const result = this.runValidationRule(rule, value, field);
      if (result !== true) {
        errors.push(result);
      }
    }

    // Update field state
    if (errors.length > 0) {
      this.showFieldError(field, errors[0]); // Show first error
      return false;
    } else {
      this.clearFieldError(field);
      if (showSuccess && value.length > 0) {
        this.showFieldSuccess(field);
      }
      return true;
    }
  }

  /**
   * Run a single validation rule
   */
  runValidationRule(rule, value, field) {
    switch (rule.type) {
      case 'required':
        return value.length > 0 || rule.message || 'This field is required';

      case 'minLength':
        return value.length >= rule.value || rule.message || `Minimum ${rule.value} characters required`;

      case 'maxLength':
        return value.length <= rule.value || rule.message || `Maximum ${rule.value} characters allowed`;

      case 'email':
        const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailPattern.test(value) || rule.message || 'Please enter a valid email address';

      case 'phone':
        return this.validateEgyptianPhone(value) || rule.message || 'Please enter a valid Egyptian phone number (Mobile: 01xxxxxxxxx, Landline: 0xxxxxxxx)';

      case 'pattern':
        const pattern = new RegExp(rule.pattern);
        return pattern.test(value) || rule.message || 'Invalid format';

      case 'custom':
        return rule.validator(value, field) || rule.message || 'Invalid value';

      default:
        return true;
    }
  }

  /**
   * Validate Egyptian phone numbers
   */
  validateEgyptianPhone(phone) {
    const cleanPhone = phone.replace(/\D/g, '');
    
    // Egyptian mobile numbers: 01xxxxxxxxx (11 digits total)
    const mobilePattern = /^01[0-9]{9}$/;
    if (mobilePattern.test(cleanPhone)) {
      return true;
    }
    
    // Egyptian landline numbers: 0xxxxxxxx (9-10 digits total)
    const landlinePattern = /^0[2-9][0-9]{7,8}$/;
    if (landlinePattern.test(cleanPhone)) {
      return true;
    }
    
    return false;
  }

  /**
   * Show field error
   */
  showFieldError(field, message) {
    this.clearFieldState(field);
    field.classList.add('form-field-error');
    
    // Create or update error message
    let errorElement = field.parentNode.querySelector('.form-error-message');
    if (!errorElement) {
      errorElement = document.createElement('span');
      errorElement.className = 'form-error-message';
      field.parentNode.appendChild(errorElement);
    }
    errorElement.textContent = message;
    
    // Add error class to form group
    const formGroup = field.closest('.form-group');
    if (formGroup) {
      formGroup.classList.add('has-error');
    }

    this.fieldErrors.set(field.id, message);
  }

  /**
   * Show field success
   */
  showFieldSuccess(field) {
    this.clearFieldState(field);
    field.classList.add('form-field-success');
    
    const formGroup = field.closest('.form-group');
    if (formGroup) {
      formGroup.classList.add('has-success');
    }
  }

  /**
   * Clear field error
   */
  clearFieldError(field) {
    this.clearFieldState(field);
    this.fieldErrors.delete(field.id);
  }

  /**
   * Clear all field states
   */
  clearFieldState(field) {
    field.classList.remove('form-field-error', 'form-field-success');
    
    // Remove error message
    const errorElement = field.parentNode.querySelector('.form-error-message');
    if (errorElement) {
      errorElement.remove();
    }
    
    // Remove form group classes
    const formGroup = field.closest('.form-group');
    if (formGroup) {
      formGroup.classList.remove('has-error', 'has-success');
    }
  }

  /**
   * Validate entire form
   * @param {HTMLFormElement} form - The form to validate
   */
  validateForm(form) {
    const fields = form.querySelectorAll('[id]');
    let isValid = true;
    const errors = [];

    fields.forEach(field => {
      if (this.validators.has(field.id)) {
        const fieldValid = this.validateField(field, true);
        if (!fieldValid) {
          isValid = false;
          const error = this.fieldErrors.get(field.id);
          if (error) {
            errors.push(error);
          }
        }
      }
    });

    return { isValid, errors };
  }

  /**
   * Clear all form errors
   */
  clearFormErrors(form) {
    const fields = form.querySelectorAll('[id]');
    fields.forEach(field => {
      this.clearFieldError(field);
    });
  }

  /**
   * Get all current errors
   */
  getAllErrors() {
    return Array.from(this.fieldErrors.values());
  }

  /**
   * Check if form has any errors
   */
  hasErrors() {
    return this.fieldErrors.size > 0;
  }
}

// Create global instance
const validationManager = new ValidationManager();

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = ValidationManager;
} else {
  window.ValidationManager = ValidationManager;
  window.validation = validationManager;
}
