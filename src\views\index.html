<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kashier Desktop</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/components.css">
    <link rel="stylesheet" href="styles/themes.css">
    <link rel="stylesheet" href="styles/notifications.css">
</head>

<body class="light-theme">
    <!-- Header -->
    <header class="app-header">
        <div class="header-content">
            <h1 class="app-title">Kashier Desktop</h1>
            <div class="header-actions">
                <button id="theme-toggle" class="btn btn-icon" title="Toggle Theme">
                    <span class="icon">🌙</span>
                </button>
                <div class="user-info">
                    <span class="user-name">Cashier</span>
                </div>
            </div>
        </div>
    </header>

    <!-- Navigation -->
    <nav class="app-nav">
        <ul class="nav-list">
            <li class="nav-item">
                <button class="nav-btn active" data-view="dashboard">
                    <span class="icon">📊</span>
                    Dashboard
                </button>
            </li>
            <li class="nav-item">
                <button class="nav-btn" data-view="customers">
                    <span class="icon">👥</span>
                    Customers
                </button>
            </li>
            <li class="nav-item">
                <button class="nav-btn" data-view="orders">
                    <span class="icon">🛒</span>
                    Orders
                </button>
            </li>
            <li class="nav-item">
                <button class="nav-btn" data-view="new-order">
                    <span class="icon">➕</span>
                    New Order
                </button>
            </li>
        </ul>
    </nav>

    <!-- Main Content -->
    <main class="app-main">
        <!-- Dashboard View -->
        <div id="dashboard-view" class="view active">
            <div class="view-header">
                <h2>Dashboard</h2>
                <div class="date-filter">
                    <input type="date" id="dashboard-date" class="form-input">
                    <button id="refresh-dashboard" class="btn btn-primary">Refresh</button>
                </div>
            </div>
            <div class="dashboard-stats">
                <div class="stat-card">
                    <h3>Today's Sales</h3>
                    <div class="stat-value" id="today-sales">$0.00</div>
                </div>
                <div class="stat-card">
                    <h3>Orders Today</h3>
                    <div class="stat-value" id="today-orders">0</div>
                </div>
                <div class="stat-card">
                    <h3>Total Customers</h3>
                    <div class="stat-value" id="total-customers">0</div>
                </div>
                <div class="stat-card">
                    <h3>Average Order</h3>
                    <div class="stat-value" id="avg-order">$0.00</div>
                </div>
            </div>
            <div class="dashboard-charts">
                <div class="chart-container">
                    <h3>Recent Orders</h3>
                    <div id="recent-orders-list" class="recent-orders">
                        <!-- Recent orders will be populated here -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Customers View -->
        <div id="customers-view" class="view">
            <div class="view-header">
                <h2>Customers</h2>
                <div class="view-actions">
                    <input type="text" id="customer-search" placeholder="Search customers..."
                        class="form-input search-input">
                    <button id="add-customer-btn" class="btn btn-primary">Add Customer</button>
                </div>
            </div>
            <div class="customers-list" id="customers-list">
                <!-- Customers will be populated here -->
            </div>
        </div>

        <!-- Orders View -->
        <div id="orders-view" class="view">
            <div class="view-header">
                <h2>Orders</h2>
                <div class="view-actions">
                    <input type="date" id="orders-date-from" class="form-input">
                    <input type="date" id="orders-date-to" class="form-input">
                    <button id="filter-orders" class="btn btn-secondary">Filter</button>
                    <button id="export-orders" class="btn btn-primary">Export</button>
                </div>
            </div>
            <div class="orders-list" id="orders-list">
                <!-- Orders will be populated here -->
            </div>
        </div>

        <!-- New Order View -->
        <div id="new-order-view" class="view">
            <div class="view-header">
                <h2>New Order</h2>
                <div class="view-actions">
                    <button id="save-order" class="btn btn-primary">Save Order</button>
                    <button id="print-receipt" class="btn btn-secondary">Print Receipt</button>
                </div>
            </div>
            <div class="order-form">
                <!-- Order form will be here -->
            </div>
        </div>
    </main>

    <!-- Modals -->
    <div id="modal-overlay" class="modal-overlay">
        <div class="modal">
            <div class="modal-header">
                <h3 id="modal-title">Modal Title</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body" id="modal-body">
                <!-- Modal content will be populated here -->
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="../models/Customer.js"></script>
    <script src="../models/Order.js"></script>
    <script src="../controllers/database.js"></script>
    <script src="../utils/helpers.js"></script>
    <script src="../utils/notifications.js"></script>
    <script src="../utils/validation.js"></script>
    <script src="../utils/phoneFormatter.js"></script>
    <script src="../utils/theme.js"></script>
    <script src="../controllers/customers.js"></script>
    <script src="../controllers/orders.js"></script>
    <script src="../controllers/dashboard.js"></script>
    <script src="scripts/app.js"></script>
</body>

</html>