/**
 * Customer Model
 * Defines the structure and validation for customer data
 */
class Customer {
  constructor(data = {}) {
    this._id = data._id || null;
    this.name = data.name || "";
    this.phone = data.phone || "";
    this.address = data.address || "";
    this.email = data.email || "";
    this.notes = data.notes || "";
    this.createdAt = data.createdAt || new Date();
    this.updatedAt = data.updatedAt || new Date();
  }

  /**
   * Validate customer data
   */
  validate() {
    const errors = [];

    // Name validation
    if (!this.name || this.name.trim().length === 0) {
      errors.push("Name is required");
    } else if (this.name.trim().length < 2) {
      errors.push("Name must be at least 2 characters long");
    } else if (this.name.trim().length > 100) {
      errors.push("Name must be less than 100 characters");
    }

    // Phone validation
    if (!this.phone || this.phone.trim().length === 0) {
      errors.push("Phone number is required");
    } else if (!this.isValidPhone(this.phone)) {
      errors.push(
        "Please enter a valid Egyptian phone number (Mobile: 01xxxxxxxxx, Landline: 0xxxxxxxx)"
      );
    }

    // Email validation (optional)
    if (this.email && this.email.trim().length > 0) {
      if (!this.isValidEmail(this.email)) {
        errors.push("Please enter a valid email address");
      }
    }

    // Address validation (optional but with length limit)
    if (this.address && this.address.length > 500) {
      errors.push("Address must be less than 500 characters");
    }

    // Notes validation (optional but with length limit)
    if (this.notes && this.notes.length > 1000) {
      errors.push("Notes must be less than 1000 characters");
    }

    return errors;
  }

  /**
   * Validate phone number format (Egyptian numbers)
   */
  isValidPhone(phone) {
    // Remove all non-digit characters
    const cleanPhone = phone.replace(/\D/g, "");

    // Egyptian mobile numbers: 01xxxxxxxxx (11 digits total)
    // Egyptian landline numbers: 0xxxxxxxx (9-10 digits total)

    // Check for Egyptian mobile numbers (01 followed by 9 digits)
    const mobilePattern = /^01[0-9]{9}$/;
    if (mobilePattern.test(cleanPhone)) {
      return true;
    }

    // Check for Egyptian landline numbers (0 followed by 8-9 digits)
    const landlinePattern = /^0[2-9][0-9]{7,8}$/;
    if (landlinePattern.test(cleanPhone)) {
      return true;
    }

    return false;
  }

  /**
   * Validate email format
   */
  isValidEmail(email) {
    const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailPattern.test(email.trim());
  }

  /**
   * Format phone number for display
   */
  getFormattedPhone() {
    const cleanPhone = this.phone.replace(/\D/g, "");

    // Format Egyptian mobile numbers: 01x xxxx xxxx
    if (cleanPhone.length === 11 && cleanPhone.startsWith("01")) {
      return `${cleanPhone.slice(0, 3)} ${cleanPhone.slice(
        3,
        7
      )} ${cleanPhone.slice(7)}`;
    }

    // Format Egyptian landline numbers: 0xx xxx xxxx or 0xx xxxx xxxx
    if (
      cleanPhone.length >= 9 &&
      cleanPhone.length <= 10 &&
      cleanPhone.startsWith("0") &&
      !cleanPhone.startsWith("01")
    ) {
      if (cleanPhone.length === 9) {
        return `${cleanPhone.slice(0, 3)} ${cleanPhone.slice(
          3,
          6
        )} ${cleanPhone.slice(6)}`;
      } else {
        return `${cleanPhone.slice(0, 3)} ${cleanPhone.slice(
          3,
          7
        )} ${cleanPhone.slice(7)}`;
      }
    }

    return this.phone; // Return original if no formatting rule matches
  }

  /**
   * Get customer's full display name with phone
   */
  getDisplayName() {
    return `${this.name} (${this.getFormattedPhone()})`;
  }

  /**
   * Convert to plain object for database storage
   */
  toObject() {
    const obj = {
      name: this.name.trim(),
      phone: this.phone.trim(),
      address: this.address.trim(),
      email: this.email.trim(),
      notes: this.notes.trim(),
      updatedAt: new Date(),
    };

    // Include ID if it exists (for updates)
    if (this._id) {
      obj._id = this._id;
    }

    // Include createdAt for new records
    if (!this._id) {
      obj.createdAt = new Date();
    }

    return obj;
  }

  /**
   * Create Customer instance from database object
   */
  static fromObject(obj) {
    return new Customer(obj);
  }

  /**
   * Search customers by name or phone
   */
  static createSearchQuery(searchTerm) {
    if (!searchTerm || searchTerm.trim().length === 0) {
      return {};
    }

    const term = searchTerm.trim();
    const regex = new RegExp(term, "i"); // Case-insensitive search

    return {
      $or: [{ name: regex }, { phone: regex }, { email: regex }],
    };
  }

  /**
   * Get default sort order for customers
   */
  static getDefaultSort() {
    return { name: 1 }; // Sort by name ascending
  }

  /**
   * Sanitize customer data before saving
   */
  sanitize() {
    this.name = this.name.trim();
    this.phone = this.phone.trim();
    this.address = this.address.trim();
    this.email = this.email.trim().toLowerCase();
    this.notes = this.notes.trim();
  }

  /**
   * Check if customer has any orders
   */
  async hasOrders() {
    try {
      const orderCount = await dbController.count("orders", {
        customerId: this._id,
      });
      return orderCount > 0;
    } catch (error) {
      console.error("Error checking customer orders:", error);
      return false;
    }
  }

  /**
   * Get customer statistics
   */
  async getStats() {
    try {
      const orders = await dbController.find("orders", {
        customerId: this._id,
      });

      const stats = {
        totalOrders: orders.length,
        totalSpent: orders.reduce((sum, order) => sum + (order.total || 0), 0),
        averageOrder: 0,
        lastOrderDate: null,
        firstOrderDate: null,
      };

      if (orders.length > 0) {
        stats.averageOrder = stats.totalSpent / stats.totalOrders;

        const sortedOrders = orders.sort(
          (a, b) => new Date(a.orderDate) - new Date(b.orderDate)
        );
        stats.firstOrderDate = sortedOrders[0].orderDate;
        stats.lastOrderDate = sortedOrders[sortedOrders.length - 1].orderDate;
      }

      return stats;
    } catch (error) {
      console.error("Error getting customer stats:", error);
      return {
        totalOrders: 0,
        totalSpent: 0,
        averageOrder: 0,
        lastOrderDate: null,
        firstOrderDate: null,
      };
    }
  }
}

// Export for use in other modules
if (typeof module !== "undefined" && module.exports) {
  module.exports = Customer;
} else {
  window.Customer = Customer;
}
