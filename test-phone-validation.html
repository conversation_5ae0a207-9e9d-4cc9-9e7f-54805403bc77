<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Phone Validation Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-input {
            width: 100%;
            padding: 10px;
            margin: 10px 0;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .valid {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .invalid {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .test-cases {
            margin: 20px 0;
        }
        .test-case {
            margin: 10px 0;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>Egyptian Phone Number Validation Test</h1>
    
    <div class="test-section">
        <h2>Interactive Test</h2>
        <label for="phone-input">Enter Egyptian Phone Number:</label>
        <input type="tel" id="phone-input" class="test-input" placeholder="Enter phone number">
        <div id="validation-result" class="result"></div>
        <div id="formatting-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>Test Cases</h2>
        <div class="test-cases" id="test-cases"></div>
    </div>

    <script>
        // Egyptian phone validation function
        function validateEgyptianPhone(phone) {
            const cleanPhone = phone.replace(/\D/g, '');
            
            // Egyptian mobile numbers: 01xxxxxxxxx (11 digits total)
            const mobilePattern = /^01[0-9]{9}$/;
            if (mobilePattern.test(cleanPhone)) {
                return { isValid: true, type: 'mobile' };
            }
            
            // Egyptian landline numbers: 0xxxxxxxx (9-10 digits total)
            const landlinePattern = /^0[2-9][0-9]{7,8}$/;
            if (landlinePattern.test(cleanPhone)) {
                return { isValid: true, type: 'landline' };
            }
            
            return { isValid: false, type: null };
        }

        // Format Egyptian phone number
        function formatEgyptianPhone(cleanPhone) {
            if (!cleanPhone) return '';
            
            // Mobile numbers: 01xxxxxxxxx
            if (cleanPhone.startsWith('01')) {
                if (cleanPhone.length <= 3) {
                    return cleanPhone;
                } else if (cleanPhone.length <= 7) {
                    return `${cleanPhone.slice(0, 3)} ${cleanPhone.slice(3)}`;
                } else if (cleanPhone.length <= 11) {
                    return `${cleanPhone.slice(0, 3)} ${cleanPhone.slice(3, 7)} ${cleanPhone.slice(7)}`;
                }
            }
            
            // Landline numbers: 0xxxxxxxx
            if (cleanPhone.startsWith('0') && !cleanPhone.startsWith('01')) {
                if (cleanPhone.length <= 3) {
                    return cleanPhone;
                } else if (cleanPhone.length <= 6) {
                    return `${cleanPhone.slice(0, 3)} ${cleanPhone.slice(3)}`;
                } else if (cleanPhone.length <= 9) {
                    return `${cleanPhone.slice(0, 3)} ${cleanPhone.slice(3, 6)} ${cleanPhone.slice(6)}`;
                } else if (cleanPhone.length <= 10) {
                    return `${cleanPhone.slice(0, 3)} ${cleanPhone.slice(3, 7)} ${cleanPhone.slice(7)}`;
                }
            }
            
            return cleanPhone;
        }

        // Test cases
        const testCases = [
            { input: '01279995486', expected: true, type: 'mobile', description: 'Valid mobile number' },
            { input: '01012345678', expected: true, type: 'mobile', description: 'Valid mobile number (Vodafone)' },
            { input: '01112345678', expected: true, type: 'mobile', description: 'Valid mobile number (Etisalat)' },
            { input: '01212345678', expected: true, type: 'mobile', description: 'Valid mobile number (Orange)' },
            { input: '0503612247', expected: true, type: 'landline', description: 'Valid landline number (9 digits)' },
            { input: '0223456789', expected: true, type: 'landline', description: 'Valid landline number (10 digits)' },
            { input: '012345', expected: false, type: null, description: 'Too short' },
            { input: '012345678901', expected: false, type: null, description: 'Too long' },
            { input: '02123456', expected: false, type: null, description: 'Landline too short' },
            { input: '021234567890', expected: false, type: null, description: 'Landline too long' },
            { input: '09123456789', expected: false, type: null, description: 'Invalid prefix' },
            { input: '1234567890', expected: false, type: null, description: 'No leading zero' },
        ];

        // Interactive testing
        const phoneInput = document.getElementById('phone-input');
        const validationResult = document.getElementById('validation-result');
        const formattingResult = document.getElementById('formatting-result');

        phoneInput.addEventListener('input', function() {
            const value = this.value;
            const cleanValue = value.replace(/\D/g, '');
            const validation = validateEgyptianPhone(value);
            const formatted = formatEgyptianPhone(cleanValue);

            // Show validation result
            if (validation.isValid) {
                validationResult.className = 'result valid';
                validationResult.textContent = `✓ Valid ${validation.type} number`;
            } else {
                validationResult.className = 'result invalid';
                validationResult.textContent = '✗ Invalid Egyptian phone number';
            }

            // Show formatting result
            formattingResult.className = 'result';
            formattingResult.textContent = `Formatted: ${formatted}`;
        });

        // Run test cases
        function runTestCases() {
            const testCasesContainer = document.getElementById('test-cases');
            
            testCases.forEach((testCase, index) => {
                const validation = validateEgyptianPhone(testCase.input);
                const passed = validation.isValid === testCase.expected && validation.type === testCase.type;
                
                const testDiv = document.createElement('div');
                testDiv.className = 'test-case';
                testDiv.innerHTML = `
                    <strong>Test ${index + 1}:</strong> ${testCase.description}<br>
                    <strong>Input:</strong> ${testCase.input}<br>
                    <strong>Expected:</strong> ${testCase.expected ? 'Valid' : 'Invalid'} ${testCase.type || ''}<br>
                    <strong>Result:</strong> ${validation.isValid ? 'Valid' : 'Invalid'} ${validation.type || ''}<br>
                    <strong>Status:</strong> <span style="color: ${passed ? 'green' : 'red'}">${passed ? 'PASS' : 'FAIL'}</span>
                `;
                
                testCasesContainer.appendChild(testDiv);
            });
        }

        // Run tests when page loads
        document.addEventListener('DOMContentLoaded', runTestCases);
    </script>
</body>
</html>
