/**
 * Customer Controller
 * Handles all customer-related operations and UI interactions
 */
class CustomerController {
  constructor() {
    console.log("Initializing Customer Controller...");
    this.customers = [];
    this.currentCustomer = null;
    this.searchTerm = "";
    this.isLoading = false;

    this.initializeEventListeners();
    this.loadCustomers();
  }

  /**
   * Initialize event listeners for customer management
   */
  initializeEventListeners() {
    // Add customer button
    const addCustomerBtn = document.getElementById("add-customer-btn");
    if (addCustomerBtn) {
      addCustomerBtn.addEventListener("click", () =>
        this.showAddCustomerModal()
      );
    }

    // Customer search
    const customerSearch = document.getElementById("customer-search");
    if (customerSearch) {
      customerSearch.addEventListener("input", (e) => {
        this.searchTerm = e.target.value;
        this.filterAndDisplayCustomers();
      });
    }

    // Modal close
    const modalClose = document.querySelector(".modal-close");
    if (modalClose) {
      modalClose.addEventListener("click", () => this.hideModal());
    }

    // Modal overlay click
    const modalOverlay = document.getElementById("modal-overlay");
    if (modalOverlay) {
      modalOverlay.addEventListener("click", (e) => {
        if (e.target === modalOverlay) {
          this.hideModal();
        }
      });
    }
  }

  /**
   * Load all customers from database
   */
  async loadCustomers() {
    try {
      this.setLoading(true);
      this.customers = await dbController.find(
        "customers",
        {},
        Customer.getDefaultSort()
      );
      this.displayCustomers();
    } catch (error) {
      console.error("Error loading customers:", error);
      this.showAlert("Error loading customers", "error");
    } finally {
      this.setLoading(false);
    }
  }

  /**
   * Filter and display customers based on search term
   */
  filterAndDisplayCustomers() {
    let filteredCustomers = this.customers;

    if (this.searchTerm.trim()) {
      const searchLower = this.searchTerm.toLowerCase();
      filteredCustomers = this.customers.filter(
        (customer) =>
          customer.name.toLowerCase().includes(searchLower) ||
          customer.phone.toLowerCase().includes(searchLower) ||
          customer.email.toLowerCase().includes(searchLower)
      );
    }

    this.displayCustomers(filteredCustomers);
  }

  /**
   * Display customers in the UI
   */
  displayCustomers(customersToShow = null) {
    const customersList = document.getElementById("customers-list");
    if (!customersList) return;

    const customers = customersToShow || this.customers;

    if (customers.length === 0) {
      customersList.innerHTML = `
                <div class="text-center" style="padding: 40px;">
                    <p class="text-muted">
                        ${
                          this.searchTerm
                            ? "No customers found matching your search."
                            : "No customers yet. Add your first customer!"
                        }
                    </p>
                </div>
            `;
      return;
    }

    customersList.innerHTML = customers
      .map(
        (customer) => `
            <div class="list-item" data-customer-id="${customer._id}">
                <div class="item-info">
                    <h4>${this.escapeHtml(customer.name)}</h4>
                    <p>
                        📞 ${this.escapeHtml(
                          this.formatPhoneForDisplay(customer.phone)
                        )}
                        ${
                          customer.email
                            ? `• ✉️ ${this.escapeHtml(customer.email)}`
                            : ""
                        }
                    </p>
                    ${
                      customer.address
                        ? `<p>📍 ${this.escapeHtml(customer.address)}</p>`
                        : ""
                    }
                </div>
                <div class="item-actions">
                    <button class="btn btn-small btn-secondary" onclick="customerController.viewCustomer('${
                      customer._id
                    }')">
                        View
                    </button>
                    <button class="btn btn-small btn-primary" onclick="customerController.editCustomer('${
                      customer._id
                    }')">
                        Edit
                    </button>
                    <button class="btn btn-small btn-danger" onclick="customerController.deleteCustomer('${
                      customer._id
                    }')">
                        Delete
                    </button>
                </div>
            </div>
        `
      )
      .join("");
  }

  /**
   * Show add customer modal
   */
  showAddCustomerModal() {
    this.currentCustomer = null;
    this.showCustomerModal("Add Customer");
  }

  /**
   * Show edit customer modal
   */
  async editCustomer(customerId) {
    try {
      this.currentCustomer = this.customers.find((c) => c._id === customerId);
      if (!this.currentCustomer) {
        this.showAlert("Customer not found", "error");
        return;
      }
      this.showCustomerModal("Edit Customer");
    } catch (error) {
      console.error("Error editing customer:", error);
      this.showAlert("Error loading customer data", "error");
    }
  }

  /**
   * Show customer modal (add/edit)
   */
  showCustomerModal(title) {
    const modalTitle = document.getElementById("modal-title");
    const modalBody = document.getElementById("modal-body");

    if (modalTitle) modalTitle.textContent = title;

    if (modalBody) {
      modalBody.innerHTML = `
                <form id="customer-form">
                    <div class="form-group">
                        <label class="form-label">Name *</label>
                        <input type="text" id="customer-name" class="form-input validate-realtime" required
                               value="${
                                 this.currentCustomer
                                   ? this.escapeHtml(this.currentCustomer.name)
                                   : ""
                               }"
                               placeholder="Enter customer name">
                    </div>

                    <div class="form-group">
                        <label class="form-label">Phone Number *</label>
                        <input type="tel" id="customer-phone" class="form-input validate-realtime phone-input" required
                               value="${
                                 this.currentCustomer
                                   ? this.escapeHtml(this.currentCustomer.phone)
                                   : ""
                               }"
                               placeholder="Enter Egyptian phone number (01xxxxxxxxx or 0xxxxxxxx)">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Email</label>
                        <input type="email" id="customer-email" class="form-input validate-realtime"
                               value="${
                                 this.currentCustomer
                                   ? this.escapeHtml(this.currentCustomer.email)
                                   : ""
                               }"
                               placeholder="Enter email address">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Address</label>
                        <textarea id="customer-address" class="form-input" rows="3" 
                                  placeholder="Enter customer address">${
                                    this.currentCustomer
                                      ? this.escapeHtml(
                                          this.currentCustomer.address
                                        )
                                      : ""
                                  }</textarea>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Notes</label>
                        <textarea id="customer-notes" class="form-input" rows="3" 
                                  placeholder="Additional notes about the customer">${
                                    this.currentCustomer
                                      ? this.escapeHtml(
                                          this.currentCustomer.notes
                                        )
                                      : ""
                                  }</textarea>
                    </div>
                    
                    <div class="flex gap-2 justify-between">
                        <button type="button" class="btn btn-secondary" onclick="customerController.hideModal()">
                            Cancel
                        </button>
                        <button type="submit" class="btn btn-primary">
                            ${this.currentCustomer ? "Update" : "Add"} Customer
                        </button>
                    </div>
                </form>
            `;
    }

    // Add form submit listener
    const customerForm = document.getElementById("customer-form");
    if (customerForm) {
      customerForm.addEventListener("submit", (e) =>
        this.handleCustomerSubmit(e)
      );
    }

    // Setup validation rules
    this.setupValidationRules();

    this.showModal();
  }

  /**
   * Setup validation rules for customer form
   */
  setupValidationRules() {
    if (window.validation) {
      // Name validation
      window.validation.registerField("customer-name", [
        { type: "required", message: "Name is required" },
        {
          type: "minLength",
          value: 2,
          message: "Name must be at least 2 characters long",
        },
        {
          type: "maxLength",
          value: 100,
          message: "Name must be less than 100 characters",
        },
      ]);

      // Phone validation
      window.validation.registerField("customer-phone", [
        { type: "required", message: "Phone number is required" },
        {
          type: "phone",
          message:
            "Please enter a valid Egyptian phone number (Mobile: 01xxxxxxxxx, Landline: 0xxxxxxxx)",
        },
      ]);

      // Email validation (optional)
      window.validation.registerField("customer-email", [
        {
          type: "custom",
          validator: (value) => {
            if (!value || value.trim().length === 0) return true; // Optional field
            return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value);
          },
          message: "Please enter a valid email address",
        },
      ]);
    }
  }

  /**
   * Handle customer form submission
   */
  async handleCustomerSubmit(e) {
    e.preventDefault();

    try {
      // Clear any existing notifications
      if (window.notifications) {
        window.notifications.clearAll();
      }

      // Validate form using the validation manager
      const form = e.target;
      if (window.validation) {
        const validationResult = window.validation.validateForm(form);
        if (!validationResult.isValid) {
          if (window.notifications) {
            window.notifications.showValidationErrors(validationResult.errors);
          } else {
            this.showAlert(validationResult.errors.join("<br>"), "error");
          }
          return;
        }
      }

      const phoneField = document.getElementById("customer-phone");
      const formData = {
        name: document.getElementById("customer-name").value,
        phone: window.phoneFormatter
          ? window.phoneFormatter.getUnformattedPhone(phoneField.value)
          : phoneField.value,
        email: document.getElementById("customer-email").value,
        address: document.getElementById("customer-address").value,
        notes: document.getElementById("customer-notes").value,
      };

      // Create customer instance for additional validation
      const customer = new Customer(formData);

      // Validate customer data (fallback validation)
      const errors = customer.validate();
      if (errors.length > 0) {
        if (window.notifications) {
          window.notifications.showValidationErrors(errors);
        } else {
          this.showAlert(errors.join("<br>"), "error");
        }
        return;
      }

      // Check for duplicate phone number
      const existingCustomer = this.customers.find(
        (c) => c.phone === customer.phone && c._id !== this.currentCustomer?._id
      );

      if (existingCustomer) {
        this.showAlert(
          "A customer with this phone number already exists",
          "error"
        );
        return;
      }

      this.setLoading(true);

      if (this.currentCustomer) {
        // Update existing customer
        customer._id = this.currentCustomer._id;
        await dbController.update(
          "customers",
          { _id: customer._id },
          customer.toObject()
        );
        this.showAlert("Customer updated successfully", "success");
      } else {
        // Add new customer
        const newCustomer = await dbController.insert(
          "customers",
          customer.toObject()
        );
        this.showAlert("Customer added successfully", "success");
      }

      this.hideModal();
      await this.loadCustomers();
    } catch (error) {
      console.error("Error saving customer:", error);
      this.showAlert("Error saving customer", "error");
    } finally {
      this.setLoading(false);
    }
  }

  /**
   * View customer details
   */
  async viewCustomer(customerId) {
    try {
      const customer = this.customers.find((c) => c._id === customerId);
      if (!customer) {
        this.showAlert("Customer not found", "error");
        return;
      }

      // Get customer statistics
      const customerInstance = Customer.fromObject(customer);
      const stats = await customerInstance.getStats();

      const modalTitle = document.getElementById("modal-title");
      const modalBody = document.getElementById("modal-body");

      if (modalTitle) modalTitle.textContent = "Customer Details";

      if (modalBody) {
        modalBody.innerHTML = `
                    <div class="customer-details">
                        <div class="form-group">
                            <strong>Name:</strong> ${this.escapeHtml(
                              customer.name
                            )}
                        </div>
                        <div class="form-group">
                            <strong>Phone:</strong> ${this.escapeHtml(
                              this.formatPhoneForDisplay(customer.phone)
                            )}
                        </div>
                        ${
                          customer.email
                            ? `<div class="form-group"><strong>Email:</strong> ${this.escapeHtml(
                                customer.email
                              )}</div>`
                            : ""
                        }
                        ${
                          customer.address
                            ? `<div class="form-group"><strong>Address:</strong> ${this.escapeHtml(
                                customer.address
                              )}</div>`
                            : ""
                        }
                        ${
                          customer.notes
                            ? `<div class="form-group"><strong>Notes:</strong> ${this.escapeHtml(
                                customer.notes
                              )}</div>`
                            : ""
                        }
                        
                        <hr style="margin: 20px 0;">
                        
                        <h4>Order Statistics</h4>
                        <div class="form-row">
                            <div><strong>Total Orders:</strong> ${
                              stats.totalOrders
                            }</div>
                            <div><strong>Total Spent:</strong> $${stats.totalSpent.toFixed(
                              2
                            )}</div>
                        </div>
                        <div class="form-row">
                            <div><strong>Average Order:</strong> $${stats.averageOrder.toFixed(
                              2
                            )}</div>
                            <div><strong>Last Order:</strong> ${
                              stats.lastOrderDate
                                ? new Date(
                                    stats.lastOrderDate
                                  ).toLocaleDateString()
                                : "Never"
                            }</div>
                        </div>
                        
                        <div class="flex gap-2 justify-between mt-4">
                            <button type="button" class="btn btn-secondary" onclick="customerController.hideModal()">
                                Close
                            </button>
                            <div class="flex gap-2">
                                <button type="button" class="btn btn-primary" onclick="customerController.editCustomer('${
                                  customer._id
                                }')">
                                    Edit Customer
                                </button>
                                <button type="button" class="btn btn-success" onclick="orderController.createOrderForCustomer('${
                                  customer._id
                                }')">
                                    New Order
                                </button>
                            </div>
                        </div>
                    </div>
                `;
      }

      this.showModal();
    } catch (error) {
      console.error("Error viewing customer:", error);
      this.showAlert("Error loading customer details", "error");
    }
  }

  /**
   * Delete customer
   */
  async deleteCustomer(customerId) {
    try {
      const customer = this.customers.find((c) => c._id === customerId);
      if (!customer) {
        this.showAlert("Customer not found", "error");
        return;
      }

      // Check if customer has orders
      const customerInstance = Customer.fromObject(customer);
      const hasOrders = await customerInstance.hasOrders();

      if (hasOrders) {
        this.showAlert("Cannot delete customer with existing orders", "error");
        return;
      }

      if (!confirm(`Are you sure you want to delete ${customer.name}?`)) {
        return;
      }

      this.setLoading(true);
      await dbController.remove("customers", { _id: customerId });
      this.showAlert("Customer deleted successfully", "success");
      await this.loadCustomers();
    } catch (error) {
      console.error("Error deleting customer:", error);
      this.showAlert("Error deleting customer", "error");
    } finally {
      this.setLoading(false);
    }
  }

  /**
   * Get customer by ID
   */
  getCustomerById(customerId) {
    return this.customers.find((c) => c._id === customerId);
  }

  /**
   * Get all customers for dropdown/selection
   */
  getCustomersForSelection() {
    return this.customers.map((customer) => ({
      id: customer._id,
      name: customer.name,
      phone: customer.phone,
      displayName: `${customer.name} (${customer.phone})`,
    }));
  }

  /**
   * Utility methods
   */
  showModal() {
    const modalOverlay = document.getElementById("modal-overlay");
    if (modalOverlay) {
      modalOverlay.classList.add("active");
    }
  }

  hideModal() {
    const modalOverlay = document.getElementById("modal-overlay");
    if (modalOverlay) {
      modalOverlay.classList.remove("active");
    }
  }

  setLoading(loading) {
    this.isLoading = loading;
    // You can add loading indicators here
  }

  showAlert(message, type = "info") {
    // Use the enhanced notification system
    if (window.notifications) {
      switch (type) {
        case "error":
          window.notifications.error(message);
          break;
        case "success":
          window.notifications.success(message);
          break;
        case "warning":
          window.notifications.warning(message);
          break;
        default:
          window.notifications.info(message);
      }
    } else {
      // Fallback to simple alert
      if (type === "error") {
        alert("Error: " + message);
      } else if (type === "success") {
        alert("Success: " + message);
      } else {
        alert(message);
      }
    }
  }

  formatPhoneForDisplay(phone) {
    if (window.phoneFormatter) {
      return window.phoneFormatter.formatEgyptianPhone(
        phone.replace(/\D/g, "")
      );
    }
    // Fallback formatting
    const cleanPhone = phone.replace(/\D/g, "");
    if (cleanPhone.length === 11 && cleanPhone.startsWith("01")) {
      return `${cleanPhone.slice(0, 3)} ${cleanPhone.slice(
        3,
        7
      )} ${cleanPhone.slice(7)}`;
    } else if (
      cleanPhone.length >= 9 &&
      cleanPhone.length <= 10 &&
      cleanPhone.startsWith("0")
    ) {
      if (cleanPhone.length === 9) {
        return `${cleanPhone.slice(0, 3)} ${cleanPhone.slice(
          3,
          6
        )} ${cleanPhone.slice(6)}`;
      } else {
        return `${cleanPhone.slice(0, 3)} ${cleanPhone.slice(
          3,
          7
        )} ${cleanPhone.slice(7)}`;
      }
    }
    return phone;
  }

  escapeHtml(text) {
    const div = document.createElement("div");
    div.textContent = text;
    return div.innerHTML;
  }
}

// Initialize customer controller when DOM is loaded
let customerController;
if (typeof document !== "undefined") {
  document.addEventListener("DOMContentLoaded", () => {
    customerController = new CustomerController();
    window.customerController = customerController;
  });
}

// Export for use in other modules
if (typeof module !== "undefined" && module.exports) {
  module.exports = CustomerController;
} else {
  window.CustomerController = CustomerController;
}
