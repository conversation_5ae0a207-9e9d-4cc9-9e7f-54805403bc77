const Datastore = require("nedb");
const path = require("path");

// Get app reference for renderer process
let electronApp;
try {
  // Try to get app from main process
  electronApp =
    require("electron").remote?.app || require("@electron/remote")?.app;
} catch (error) {
  console.log("Running in renderer process, app reference not available");
  electronApp = null;
}

/**
 * Database Controller
 * Manages all database operations using NeDB
 */
class DatabaseController {
  constructor() {
    console.log("Initializing Database Controller...");
    this.databases = {};
    this.isInitialized = false;
    this.initializeDatabases();
  }

  /**
   * Initialize all databases
   */
  initializeDatabases() {
    try {
      // Get user data directory for storing databases
      // Use data folder in project directory for now
      const userDataPath = path.join(__dirname, "../../data");

      // Initialize customers database
      this.databases.customers = new Datastore({
        filename: path.join(userDataPath, "customers.db"),
        autoload: true,
        timestampData: true,
      });

      // Initialize orders database
      this.databases.orders = new Datastore({
        filename: path.join(userDataPath, "orders.db"),
        autoload: true,
        timestampData: true,
      });

      // Initialize settings database
      this.databases.settings = new Datastore({
        filename: path.join(userDataPath, "settings.db"),
        autoload: true,
        timestampData: true,
      });

      // Create indexes for better performance
      this.createIndexes();

      this.isInitialized = true;
      console.log("Databases initialized successfully");
    } catch (error) {
      console.error("Error initializing databases:", error);
      throw error;
    }
  }

  /**
   * Create database indexes for better performance
   */
  createIndexes() {
    // Customer indexes
    this.databases.customers.ensureIndex({ fieldName: "phone", unique: true });
    this.databases.customers.ensureIndex({ fieldName: "name" });

    // Order indexes
    this.databases.orders.ensureIndex({ fieldName: "customerId" });
    this.databases.orders.ensureIndex({ fieldName: "orderDate" });
    this.databases.orders.ensureIndex({ fieldName: "status" });

    // Settings indexes
    this.databases.settings.ensureIndex({ fieldName: "key", unique: true });
  }

  /**
   * Generic method to insert a document
   */
  async insert(collection, document) {
    return new Promise((resolve, reject) => {
      if (!this.databases[collection]) {
        reject(new Error(`Collection ${collection} not found`));
        return;
      }

      this.databases[collection].insert(document, (err, newDoc) => {
        if (err) {
          reject(err);
        } else {
          resolve(newDoc);
        }
      });
    });
  }

  /**
   * Generic method to find documents
   */
  async find(collection, query = {}, sort = {}) {
    return new Promise((resolve, reject) => {
      if (!this.databases[collection]) {
        reject(new Error(`Collection ${collection} not found`));
        return;
      }

      let cursor = this.databases[collection].find(query);

      if (Object.keys(sort).length > 0) {
        cursor = cursor.sort(sort);
      }

      cursor.exec((err, docs) => {
        if (err) {
          reject(err);
        } else {
          resolve(docs);
        }
      });
    });
  }

  /**
   * Generic method to find one document
   */
  async findOne(collection, query) {
    return new Promise((resolve, reject) => {
      if (!this.databases[collection]) {
        reject(new Error(`Collection ${collection} not found`));
        return;
      }

      this.databases[collection].findOne(query, (err, doc) => {
        if (err) {
          reject(err);
        } else {
          resolve(doc);
        }
      });
    });
  }

  /**
   * Generic method to update documents
   */
  async update(collection, query, update, options = {}) {
    return new Promise((resolve, reject) => {
      if (!this.databases[collection]) {
        reject(new Error(`Collection ${collection} not found`));
        return;
      }

      this.databases[collection].update(
        query,
        update,
        options,
        (err, numReplaced) => {
          if (err) {
            reject(err);
          } else {
            resolve(numReplaced);
          }
        }
      );
    });
  }

  /**
   * Generic method to remove documents
   */
  async remove(collection, query, options = {}) {
    return new Promise((resolve, reject) => {
      if (!this.databases[collection]) {
        reject(new Error(`Collection ${collection} not found`));
        return;
      }

      this.databases[collection].remove(query, options, (err, numRemoved) => {
        if (err) {
          reject(err);
        } else {
          resolve(numRemoved);
        }
      });
    });
  }

  /**
   * Count documents in a collection
   */
  async count(collection, query = {}) {
    return new Promise((resolve, reject) => {
      if (!this.databases[collection]) {
        reject(new Error(`Collection ${collection} not found`));
        return;
      }

      this.databases[collection].count(query, (err, count) => {
        if (err) {
          reject(err);
        } else {
          resolve(count);
        }
      });
    });
  }

  /**
   * Get database statistics
   */
  async getStats() {
    try {
      const stats = {
        customers: await this.count("customers"),
        orders: await this.count("orders"),
        settings: await this.count("settings"),
      };
      return stats;
    } catch (error) {
      console.error("Error getting database stats:", error);
      throw error;
    }
  }

  /**
   * Backup all databases
   */
  async backup() {
    try {
      const backup = {
        customers: await this.find("customers"),
        orders: await this.find("orders"),
        settings: await this.find("settings"),
        timestamp: new Date().toISOString(),
      };
      return backup;
    } catch (error) {
      console.error("Error creating backup:", error);
      throw error;
    }
  }

  /**
   * Restore from backup
   */
  async restore(backupData) {
    try {
      // Clear existing data
      await this.remove("customers", {}, { multi: true });
      await this.remove("orders", {}, { multi: true });
      await this.remove("settings", {}, { multi: true });

      // Restore data
      if (backupData.customers) {
        for (const customer of backupData.customers) {
          delete customer._id; // Remove old ID to generate new one
          await this.insert("customers", customer);
        }
      }

      if (backupData.orders) {
        for (const order of backupData.orders) {
          delete order._id; // Remove old ID to generate new one
          await this.insert("orders", order);
        }
      }

      if (backupData.settings) {
        for (const setting of backupData.settings) {
          delete setting._id; // Remove old ID to generate new one
          await this.insert("settings", setting);
        }
      }

      console.log("Database restored successfully");
    } catch (error) {
      console.error("Error restoring database:", error);
      throw error;
    }
  }
}

// Create singleton instance
const dbController = new DatabaseController();

// Export for use in other modules
if (typeof module !== "undefined" && module.exports) {
  module.exports = dbController;
} else {
  window.dbController = dbController;
}
